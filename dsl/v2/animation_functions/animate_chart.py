"""
effect: |
  创建并播放条形图、折线图或雷达图的动画，支持单个或多个数据集。

use_cases:
  - 可视化数据趋势和比较（折线图、条形图）
  - 展示多个类别在不同指标上的表现（雷达图）
  - 在视频演示中动态呈现统计数据
  - 对比不同数据集之间的关系

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  chart_type:
    type: str
    desc: 图表类型。可选值：'bar'（条形图）, 'line'（折线图）, 'radar'（雷达图）
    required: true
  data:
    type: dict | list[dict]
    desc: 图表数据。单数据集为dict格式（如{"A":10,"B":20}），多数据集为dict列表
    required: true
  narration:
    type: str
    desc: 在图表显示时播放的语音旁白文本
    required: true
  title:
    type: str
    desc: 图表标题
    default: None
  animation_style:
    type: str
    desc: 图表入场动画。可选值：'fadeIn', 'grow', 'draw', 'update'
    default: fadeIn
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  dataset_names:
    type: list[str]
    desc: 多数据集时，用于图例的数据集名称列表
    default: None
  x_label:
    type: str
    desc: x轴标签（条形图和折线图有效）
    default: None
  y_label:
    type: str
    desc: y轴标签（条形图和折线图有效）
    default: None

dsl_examples:
  - type: animate_chart
    params:
      chart_type: bar
      data:
        苹果: 75
        香蕉: 120
        橙子: 90
      title: 水果销量
      narration: 这是本月水果销量的条形图。
      x_label: 水果
      y_label: 销量 (千克)
  - type: animate_chart
    params:
      chart_type: line
      data:
        - 第一季度: 50
          第二季度: 65
          第三季度: 80
          第四季度: 70
        - 第一季度: 40
          第二季度: 50
          第三季度: 60
          第四季度: 90
      title: 产品A vs 产品B 季度销售额
      dataset_names: ["产品A", "产品B"]
      narration: 此折线图比较了产品A和产品B的季度销售额。
      x_label: 季度
      y_label: 销售额 (万元)
  - type: animate_chart
    params:
      chart_type: radar
      data:
        - 性能: 8
          价格: 6
          外观: 9
          易用性: 7
          可靠性: 8
        - 性能: 9
          价格: 4
          外观: 7
          易用性: 8
          可靠性: 9
      title: 产品对比
      dataset_names: ["产品X", "产品Y"]
      narration: 这个雷达图展示了两款产品在五个维度上的评分对比。

notes:
  - 对于条形图和折线图，数据键作为x轴标签，值作为y轴数据点
  - 对于雷达图，数据键作为各个轴的标签，值作为该轴上的数据点
  - 如果提供多个数据集，建议同时提供dataset_names以便在图例中显示
  - update动画风格对条形图有特殊效果，会显示数值从0到目标值的变化过程
"""

from typing import TYPE_CHECKING, Optional

import numpy as np
from loguru import logger
from manim import *

from dsl.v2.themes.theme_utils import ThemeUtils

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene


def _get_colors(num_datasets: int) -> list[str]:
    """Generate a list of different colors for multiple datasets.

    Args:
        num_datasets: Number of datasets to generate colors for

    Returns:
        List of color constants
    """
    # 使用主题系统的图表颜色
    return ThemeUtils.get_cyclic_colors(num_datasets)


def _normalize_data(data: dict | list[dict]) -> tuple[list[dict], list[str]]:
    """Normalize data into a list format and return dataset names.

    Args:
        data: Data in dict or list of dicts format

    Returns:
        Tuple of (normalized data list, dataset names list)
    """
    if isinstance(data, dict):
        # If it's a single dict, wrap it in a list
        normalized_data = [data]
        dataset_names = ["数据集1"]  # Default dataset name
    else:
        normalized_data = data
        # Use indices as default dataset names
        dataset_names = [f"数据集{i+1}" for i in range(len(data))]

    return normalized_data, dataset_names


def _create_bar_chart(
    normalized_data: list[dict],
    colors: list[str],
    x_label: str = None,
    y_label: str = None,
) -> VGroup:
    """Create a bar chart with multiple datasets.

    Args:
        normalized_data: List of data dictionaries
        colors: List of colors for different datasets
        x_label: Label for the x-axis
        y_label: Label for the y-axis

    Returns:
        VGroup containing the complete bar chart
    """
    # Prepare all values to find the maximum
    labels = list(normalized_data[0].keys())
    all_values = []
    for dataset in normalized_data:
        all_values.extend(list(dataset.values()))

    max_y = 1.2 * max(all_values)
    # Round max_y up to the nearest integer to ensure integer y-axis values
    max_y = int(max_y) + 1

    # Create the first dataset's bars to get axes and layout
    data_0_values = list(normalized_data[0].values())

    y_range = [0, max_y]
    # 使用主题系统的图表样式
    bar_width = ThemeUtils.get_component_style("chart", "chart_bar_width", 0.3)
    x_length = 10
    y_length = 6.5

    # 使用主题系统的线条宽度
    axis_stroke_width = ThemeUtils.get_component_style("chart", "axis_stroke_width", 3.0)

    # Create base chart for axes and layout
    # 使用主题字体和字号
    body_font_size = ThemeUtils.get_font_size("body")

    # 获取主题文本颜色
    text_color = ThemeUtils.get_color("text_primary")
    axis_color = ThemeUtils.get_color("text_secondary")

    base_chart = BarChart(
        values=data_0_values,
        bar_names=None,  # Don't use built-in labels
        y_range=y_range,
        bar_colors=[colors[0]],
        bar_width=bar_width,
        x_length=x_length,
        y_length=y_length,
        stroke_width=axis_stroke_width,
        y_axis_config={"font_size": body_font_size},
        axis_config={"color": axis_color},
    )
    base_chart.y_axis.set_color(axis_color)
    base_chart.x_axis.set_color(axis_color)

    # Create a group for all bars
    all_bars = VGroup()
    all_bars.add(*base_chart.bars)  # Add first dataset bars

    # Process additional datasets
    n_datasets = len(normalized_data)
    for i in range(1, n_datasets):
        dataset_values = list(normalized_data[i].values())

        # Create chart but don't add to scene
        dataset_chart = BarChart(
            values=dataset_values,
            bar_names=None,
            y_range=y_range,
            bar_colors=[colors[i]],
            bar_width=bar_width,
            x_length=x_length,
            y_length=y_length,
            y_axis_config={"font_size": body_font_size},
            axis_config={"color": axis_color},
        )

        # Adjust positions of bars for this dataset
        shift_value = i - (n_datasets - 1) / 2
        offset = shift_value * 0.7  # Appropriate offset

        for j, bar in enumerate(dataset_chart.bars):
            # Get corresponding original position
            base_bar = base_chart.bars[j]
            # Calculate bottom position to align with x-axis
            base_bottom = base_bar.get_bottom()
            # Move to aligned position, keeping y consistent, adjusting x
            bar_height = bar.height
            bar_center_x = base_bar.get_center()[0] + offset
            bar_center_y = base_bottom[1] + bar_height / 2
            bar.move_to([bar_center_x, bar_center_y, 0])

        all_bars.add(*dataset_chart.bars)

    # Get axes from base chart
    axes = base_chart.axes

    # Create X-axis labels
    x_labels = VGroup()
    for i, label in enumerate(labels):
        # Use bar positions to place labels
        bar_center = base_chart.bars[i].get_center()
        x_axis_height = base_chart.x_axis.get_center()[1]  # Get x-axis height
        # 使用主题系统的文本样式
        font = ThemeUtils.get_font("primary")
        font_size = ThemeUtils.get_font_size("body")
        text_color = ThemeUtils.get_color("text_primary")
        label_obj = Text(label, font_size=font_size, font=font, color=text_color)
        # Place label below the bar at x-axis
        label_obj.move_to([bar_center[0], x_axis_height, 0])
        label_obj.shift(DOWN * ThemeUtils.get_spacing("sm"))  # 使用主题系统的间距
        x_labels.add(label_obj)

    # Add axis unit labels if provided
    if x_label is not None:
        # 使用主题系统创建文本
        x_axis_label = ThemeUtils.create_themed_text(x_label, "body", "text_primary")
        x_axis_label.next_to(base_chart.x_axis, DOWN, buff=ThemeUtils.get_spacing("lg"))
        x_labels.add(x_axis_label)

    if y_label is not None:
        # 使用主题系统创建文本
        y_axis_label = ThemeUtils.create_themed_text(y_label, "body", "text_primary")
        y_axis_label.next_to(base_chart.y_axis, LEFT, buff=ThemeUtils.get_spacing("sm"))
        y_axis_label.rotate(90 * DEGREES)
        axes.add(y_axis_label)

    # Combine complete chart
    chart = VGroup(axes, all_bars, x_labels)
    return chart


def _create_line_chart(
    normalized_data: list[dict],
    colors: list[str],
    x_label: str = None,
    y_label: str = None,
) -> VGroup:
    """Create a line chart with multiple datasets.

    Args:
        normalized_data: List of data dictionaries
        colors: List of colors for different datasets
        x_label: Label for the x-axis
        y_label: Label for the y-axis
    Returns:
        VGroup containing the complete line chart
    """
    # Calculate all values to find max for y-axis
    labels = list(normalized_data[0].keys())
    all_values = []
    for dataset in normalized_data:
        all_values.extend(list(dataset.values()))

    # Create X values array (same for all datasets)
    x_vals = list(range(len(labels)))

    # Set coordinate ranges
    max_y = max(all_values) * 1.2
    min_y = 0
    y_step = max(1, int(max_y / 5))  # Limit to at most 5 major ticks

    x_range = [0, len(x_vals) - 1, 1]
    y_range = [min_y, max_y, y_step]

    # Create axes
    axes = Axes(
        x_range=x_range,
        y_range=y_range,
        axis_config={
            "color": ThemeUtils.get_color("text_primary"),
            "stroke_width": ThemeUtils.get_component_style("chart", "axis_stroke_width", 3.0),
        },
    )

    # Add y-axis tick labels
    for y_val in range(min_y, int(max_y) + 1, y_step):
        label_pos = axes.c2p(0, y_val)
        font = ThemeUtils.get_font("primary")
        font_size = ThemeUtils.get_font_size("body")
        text_color = ThemeUtils.get_color("text_primary")
        yl = Text(str(y_val), font_size=font_size, color=text_color, font=font)
        yl.next_to(label_pos, LEFT, buff=ThemeUtils.get_spacing("sm"))
        axes.add(yl)

    # Define different dot styles
    dot_styles = [
        {"fill_color": colors[0], "stroke_width": 1},
        {"fill_color": colors[1 % len(colors)], "stroke_width": 1},
        {"fill_color": colors[2 % len(colors)], "stroke_width": 1},
        {"fill_color": colors[3 % len(colors)], "stroke_width": 1},
    ]

    # Draw line graphs for each dataset
    line_graphs = []
    for i, dataset in enumerate(normalized_data):
        dataset_values = list(dataset.values())

        # Draw line graph
        line_graph = axes.plot_line_graph(
            x_values=x_vals,
            y_values=dataset_values,
            line_color=colors[i % len(colors)],
            vertex_dot_radius=0.12,
            vertex_dot_style=dot_styles[i % len(dot_styles)],
            stroke_width=ThemeUtils.get_component_style("chart", "chart_line_width", 4.0),
        )

        line_graphs.append(line_graph)

    # Add X-axis labels
    for i, label in enumerate(labels):
        label_pos = axes.c2p(i, 0)
        font = ThemeUtils.get_font("primary")
        font_size = ThemeUtils.get_font_size("body")
        text_color = ThemeUtils.get_color("text_primary")
        label_obj = Text(label, font_size=font_size, font=font, color=text_color)
        label_obj.next_to(label_pos, DOWN, buff=ThemeUtils.get_spacing("sm"))
        axes.add(label_obj)

    # Add axis unit labels if provided
    if x_label is not None:
        x_axis_label = ThemeUtils.create_themed_text(x_label, "body", "text_primary")
        x_axis_label.next_to(axes, DOWN, buff=ThemeUtils.get_spacing("lg"))
        axes.add(x_axis_label)

    if y_label is not None:
        y_axis_label = ThemeUtils.create_themed_text(y_label, "body", "text_primary")
        y_axis_label.rotate(90 * DEGREES)
        y_axis_label.next_to(axes, LEFT, buff=ThemeUtils.get_spacing("sm"))
        axes.add(y_axis_label)

    # Combine chart, including axes and all line graphs
    chart = VGroup(axes, *line_graphs)
    return chart


def _create_radar_chart(
    normalized_data: list[dict],
    colors: list[str],
) -> VGroup:
    """Create a radar chart with multiple datasets.

    Args:
        normalized_data: List of data dictionaries
        colors: List of colors for different datasets

    Returns:
        VGroup containing the complete radar chart
    """
    # Calculate max value for normalization
    labels = list(normalized_data[0].keys())
    all_values = []
    for dataset in normalized_data:
        all_values.extend(list(dataset.values()))
    logger.info(f"all_values: {all_values}")
    max_value = max(all_values)

    # Set radar chart parameters
    n = len(labels)
    radius = 2.5  # Initial radius, will be scaled later if needed
    center = ORIGIN

    # Create angles for the radar chart axes
    angles = [i * 2 * np.pi / n for i in range(n)]
    points = [center + radius * np.array([np.cos(angle), np.sin(angle), 0]) for angle in angles]

    # Create background polygon
    bg_polygon = Polygon(
        *points,
        color=ThemeUtils.get_color("text_primary"),
        stroke_width=ThemeUtils.get_component_style("chart", "axis_stroke_width", 1.0),
    )

    # Add concentric circle ticks
    circles = []
    for i in range(1, 5):  # Add 4 concentric circles
        ratio = i / 4
        circle_points = [
            center + radius * ratio * np.array([np.cos(angle), np.sin(angle), 0])
            for angle in np.linspace(0, 2 * np.pi, 50)
        ]
        circle = Polygon(
            *circle_points,
            color=ThemeUtils.get_color("text_secondary"),
            stroke_width=0.5,
            stroke_opacity=0.5,
            fill_opacity=0,
        )
        circles.append(circle)

    # Create axis lines
    axes = []
    for point in points:
        axes.append(
            Line(
                center,
                point,
                color=ThemeUtils.get_color("text_primary"),
                stroke_width=ThemeUtils.get_component_style("chart", "axis_stroke_width", 1.0),
            )
        )

    # Create polygons for each dataset
    data_polygons = []
    data_dots = []

    # Add axis labels
    label_objs = []
    label_backgrounds = []

    for i, label in enumerate(labels):
        # Calculate label position (outside the axis endpoint)
        angle = angles[i]
        # Use angle to correctly place label, and increase distance to avoid overlap
        direction = np.array([np.cos(angle), np.sin(angle), 0])
        # Dynamically adjust distance based on angle and label length
        # Especially increase distance for bottom labels
        is_bottom_label = np.pi * 0.4 < angle < np.pi * 1.6  # Identify labels in bottom area
        distance_factor = 0.8 if is_bottom_label else (0.7 if len(label) > 3 else 0.6)
        label_pos = center + (radius + distance_factor) * direction
        # Create label with Text, supporting Chinese
        font = ThemeUtils.get_font("primary")
        font_size = ThemeUtils.get_font_size("small")
        text_color = ThemeUtils.get_color("text_primary")
        label_obj = Text(label, font_size=font_size, font=font, color=text_color)
        label_obj.move_to(label_pos)
        # Add semi-transparent background box for better readability
        padding = 0.1
        background = Rectangle(
            height=label_obj.height + padding * 2,
            width=label_obj.width + padding * 2,
            fill_color=ThemeUtils.get_color("background"),
            fill_opacity=ThemeUtils.get_component_style("background", "background_opacity", 0.6),
            stroke_opacity=0,
        )
        background.move_to(label_obj.get_center())
        label_backgrounds.append(background)
        label_objs.append(label_obj)

    # Create value labels and backgrounds
    value_labels = []
    value_backgrounds = []

    # Create data polygons for each dataset
    for i, dataset in enumerate(normalized_data):
        dataset_values = list(dataset.values())

        # Scale values relative to max
        scaled_values = [val / max_value for val in dataset_values]

        # Calculate points for this dataset
        data_points = [
            center + radius * scaled_values[j] * np.array([np.cos(angles[j]), np.sin(angles[j]), 0]) for j in range(n)
        ]

        # Create polygon
        polygon = Polygon(
            *data_points,
            color=colors[i % len(colors)],
            fill_opacity=ThemeUtils.get_component_style("background", "background_opacity", 0.3),
            stroke_width=ThemeUtils.get_component_style("chart", "chart_line_width", 2.0),
        )
        data_polygons.append(polygon)

        # Add data points
        dots = []
        for point in data_points:
            dot = Dot(point, color=colors[i % len(colors)], radius=0.1)
            dots.append(dot)
        data_dots.extend(dots)

        # Add value labels
        for j, val in enumerate(dataset_values):
            # Create value label
            font = ThemeUtils.get_font("primary")
            font_size = ThemeUtils.get_font_size("small")
            value_label = Text(str(val), font_size=font_size, color=colors[i % len(colors)], font=font)

            # Calculate label position
            angle = angles[j]
            scaled_value = scaled_values[j]
            direction = np.array([np.cos(angle), np.sin(angle), 0])
            data_point = center + radius * scaled_value * direction

            # Intelligently adjust label offset to avoid overlaps
            if scaled_value > 0.8:  # Near outer circle
                # Offset inward to avoid axis label overlap
                offset_direction = -0.3 * direction + 0.2 * np.array([-direction[1], direction[0], 0])
            elif scaled_value > 0.5:  # Medium distance
                # Lateral offset
                offset_direction = 0.1 * direction + 0.25 * np.array([-direction[1], direction[0], 0])
            else:  # Near center
                # Radial offset
                offset_direction = 0.3 * direction

            value_label.move_to(data_point + offset_direction)

            # Add semi-transparent background
            padding = 0.05
            value_bg = Rectangle(
                height=value_label.height + padding * 2,
                width=value_label.width + padding * 2,
                fill_color=ThemeUtils.get_color("background"),
                fill_opacity=ThemeUtils.get_component_style("background", "background_opacity", 0.6),
                stroke_opacity=0,
            )
            value_bg.move_to(value_label.get_center())
            value_backgrounds.append(value_bg)
            value_labels.append(value_label)

    # Combine all elements in visual order
    radar_elements = VGroup(
        bg_polygon,  # Background polygon at bottom
        *circles,  # Concentric circle ticks
        *axes,  # Axis lines
        *data_polygons,  # Data polygons
        *data_dots,  # Data points
    )

    # Add label backgrounds and labels
    if label_backgrounds:
        radar_elements.add(*label_backgrounds)
    if label_objs:
        radar_elements.add(*label_objs)

    # Add value label backgrounds and value labels
    if value_backgrounds:
        radar_elements.add(*value_backgrounds)
    if value_labels:
        radar_elements.add(*value_labels)

    return radar_elements


def _create_legend(
    dataset_names: list[str],
    colors: list[str],
) -> VGroup:
    """Create a legend for multiple datasets.

    Args:
        dataset_names: List of dataset names
        colors: List of colors for different datasets

    Returns:
        VGroup containing the legend
    """
    legend_items = []

    for i, name in enumerate(dataset_names):
        # Create color rectangle
        color_rect = Rectangle(
            height=0.2,
            width=0.4,
            fill_opacity=1,
            fill_color=colors[i],
            stroke_width=ThemeUtils.get_component_style("chart", "chart_line_width", 1.0),
        )
        # Create text label
        font = ThemeUtils.get_font("primary")
        font_size = ThemeUtils.get_font_size("small")
        text_color = ThemeUtils.get_color("text_primary")
        label = Text(name, font_size=font_size, font=font, color=text_color)
        # Combine rectangle and label
        label.next_to(color_rect, RIGHT, buff=0.1)
        legend_item = VGroup(color_rect, label)
        legend_items.append(legend_item)

    # Arrange legend items
    legend = VGroup()
    for i, item in enumerate(legend_items):
        if i == 0:
            legend.add(item)
        else:
            # Place new item to the right of previous items
            item.next_to(legend, RIGHT, buff=0.3)
            legend.add(item)

    return legend


def animate_chart(
    scene: "FeynmanScene",
    chart_type: str,  # 'bar', 'line', or 'radar'
    data: dict | list[dict],
    title: Optional[str] = None,
    animation_style: str = "fadeIn",  # 'fadeIn', 'grow', 'draw', or 'update'
    narration: Optional[str] = None,
    id: Optional[str] = None,
    dataset_names: Optional[list[str]] = None,
    x_label: Optional[str] = None,
    y_label: Optional[str] = None,
):
    logger.info(f"Animating chart '{id}' of type '{chart_type}' in region 'full_screen'.")
    scene.clear_current_mobj()

    # Generate a unique ID if not provided
    unique_id = id or f"chart_{abs(hash(str(data))) % 10000}"

    # Normalize data structure
    normalized_data, default_dataset_names = _normalize_data(data)

    # Use provided dataset names or defaults
    actual_dataset_names = dataset_names or default_dataset_names

    # Generate colors for datasets
    colors = _get_colors(len(normalized_data))

    # Create title if provided
    title_obj = None
    if title:
        # 使用主题系统创建标题文本
        title_obj = ThemeUtils.create_themed_text(title, "h2", "primary")
        title_obj.to_edge(UP)

    # Create chart based on type
    if chart_type == "bar":
        chart = _create_bar_chart(normalized_data, colors, x_label, y_label)
    elif chart_type == "line":
        chart = _create_line_chart(normalized_data, colors, x_label, y_label)
    elif chart_type == "radar":
        chart = _create_radar_chart(normalized_data, colors)
    else:
        logger.error(f"Unsupported chart type: {chart_type}")
        return

    # Create legend
    legend = _create_legend(actual_dataset_names, colors)

    # Combine all elements into a group
    if title_obj:
        # Set legend position below title
        legend.next_to(title_obj, DOWN, buff=0.3)
        chart.next_to(legend, DOWN, buff=0.3)
        group = VGroup(chart, legend, title_obj)
    else:
        # Set legend position at top
        legend.to_edge(UP, buff=0.3)
        chart.next_to(legend, DOWN, buff=0.3)
        group = VGroup(chart, legend)

    # Ensure chart fits the screen
    target_rect = scene.full_screen_rect
    if target_rect:
        # Calculate available space
        available_width = target_rect.width * 0.8  # Reduced from 0.95 to 0.8
        available_height = target_rect.height * 0.8  # Reduced from 0.95 to 0.8

        # Scale if necessary
        if group.width > 0 and group.height > 0:
            scale_factor = min(available_width / group.width, available_height / group.height)
            if scale_factor < 1.0:  # Only scale down, not up
                group.scale(scale_factor)

            # Center in target region
            group.move_to(target_rect.get_center())

    # Animate the chart with or without narration
    with scene.voiceover(text=narration) as tracker:
        narration_duration = tracker.duration if hasattr(tracker, "duration") else 0

        # 获取主题系统中的动画持续时间设置
        fade_duration = ThemeUtils.get_animation_duration("fade")
        default_duration = ThemeUtils.get_animation_duration("default")

        if animation_style == "grow":
            if title_obj:
                scene.play(FadeIn(title_obj), run_time=fade_duration)
            scene.play(FadeIn(legend), run_time=fade_duration * 0.7)
            scene.play(Create(chart), run_time=default_duration)

        elif animation_style == "fadeIn":
            scene.play(FadeIn(group), run_time=fade_duration)

        elif animation_style == "draw":
            if title_obj:
                scene.play(Write(title_obj), run_time=fade_duration)
            scene.play(FadeIn(legend), run_time=fade_duration * 0.7)
            scene.play(Create(chart), run_time=default_duration)

        elif animation_style == "update":
            if chart_type == "bar":
                if title_obj:
                    scene.play(FadeIn(title_obj), run_time=fade_duration)
                scene.play(FadeIn(legend), run_time=fade_duration * 0.7)
                scene.play(Create(chart), run_time=default_duration * 0.8)
            else:
                if title_obj:
                    scene.play(FadeIn(title_obj), run_time=fade_duration)
                scene.play(Create(chart), run_time=default_duration)
                scene.play(FadeIn(legend), run_time=fade_duration * 0.7)

        # Wait for narration to complete if needed
        effective_duration = max(3.0, narration_duration) - 3.8  # Subtract animation time
        if effective_duration > 0:
            scene.wait(effective_duration)

    # Update region content
    scene.current_mobj = group
    logger.info(f"Completed animating chart '{unique_id}' in region 'full_screen'")
