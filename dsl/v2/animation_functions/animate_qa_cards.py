"""
effect: |
    创建完全按照现代HTML设计风格的QA卡片展示动画，复刻Prismatic Focus Q&A Cards的每一个视觉细节

use_cases:
    - 教育培训中的问答展示
    - 知识总结和回顾
    - 产品FAQ展示
    - 学术论文要点问答
    - 面试问题练习

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  qa_data:
    type: list
    desc: QA数据列表，每个元素包含question和answer字段
    required: true
  title:
    type: str
    desc: 展示标题
    required: false
    default: "Prismatic Focus Q&A Cards"
  cards_per_screen:
    type: int
    desc: 每屏显示的卡片数量（建议最多3个）
    required: false
    default: 3
  duration_per_card:
    type: float
    desc: 每张卡片的展示时长（秒）
    required: false
    default: 4.0
  narration:
    type: str
    desc: 语音旁白内容
    required: true

dsl_examples:
  - type: animate_qa_cards
    params:
      qa_data:
        - question: "什么是'第一性原理'思维？"
          answer: "第一性原理是一种解决问题的思维方式，它强调回归事物的本质和基本公理进行分析，而不是依赖类比或既有经验。"
        - question: "什么是'SMART原则'？"
          answer: "SMART原则是设定目标的经典方法论，确保目标清晰可行。它代表：具体的、可衡量的、可达成的、相关的和有时限的。"
        - question: "什么是'心流'状态？"
          answer: "心流是一种个体完全沉浸、全神贯注于某项活动时的心理状态。在这种状态下，人会感到极大的愉悦和满足。"
      title: "知识问答卡片"
      narration: "让我们通过这些精美的卡片来学习重要概念"

notes:
  - 完全复刻HTML版本的视觉设计
  - 使用精确的颜色和布局（内置三种颜色主题：靛蓝、松石绿、活力橙）
  - 支持响应式设计理念
  - 包含顶部彩色强调条和圆形图标
  - 现代化的卡片阴影和悬停效果
  - 智能自动换行功能，适应长文本内容
"""

import logging

from manim import *

from dsl.v2.core.scene import FeynmanScene

logger = logging.getLogger(__name__)


def animate_qa_cards(
    scene: FeynmanScene,
    qa_data: list[dict[str, str]],
    title: str = "Prismatic Focus Q&A Cards",
    cards_per_screen: int = 3,
    duration_per_card: float = 4.0,
    narration: str = "让我们来看看这些精美的问答卡片",
) -> None:
    """
    创建完全按照HTML设计的现代QA卡片展示动画
    """
    logger.info(f"开始创建HTML风格QA卡片动画，共{len(qa_data)}个问答对")

    try:
        scene.clear_current_mobj()

        # 设置浅灰色背景，模拟HTML的 #F7FAFC
        scene.camera.background_color = "#F7FAFC"

        with scene.voiceover(narration) as tracker:  # noqa: F841
            # 创建标题 - 使用Inter字体风格
            title_text = Text(
                title,
                font_size=44,
                color="#1A202C",  # 深色文字
                font="Arial",
                weight=BOLD,
            )
            title_text.to_edge(UP, buff=0.8)
            scene.play(Create(title_text), run_time=1.0)
            scene.wait(0.5)

            # 计算需要的屏幕数
            total_screens = (len(qa_data) + cards_per_screen - 1) // cards_per_screen

            for screen_idx in range(total_screens):
                start_idx = screen_idx * cards_per_screen
                end_idx = min(start_idx + cards_per_screen, len(qa_data))
                screen_qa_data = qa_data[start_idx:end_idx]

                # 创建当前屏幕的卡片
                cards = []
                card_group = VGroup()

                for i, qa in enumerate(screen_qa_data):
                    card = create_html_style_card(
                        qa["question"],
                        qa["answer"],
                        i,  # 卡片索引用于颜色主题
                        len(screen_qa_data),
                    )
                    cards.append(card)
                    card_group.add(card)

                # 横向排列卡片 - 模拟HTML flexbox布局
                if len(screen_qa_data) == 1:
                    card_group.move_to(ORIGIN + DOWN * 0.3)
                else:
                    # 减小间距以容纳更大的卡片
                    card_group.arrange(RIGHT, buff=0.3)
                    card_group.move_to(ORIGIN + DOWN * 0.3)

                    # 放宽屏幕边界限制，允许更大的卡片（最大宽度约14单位）
                    if card_group.get_width() > 14:
                        card_group.scale_to_fit_width(14)

                # 应用优雅的入场动画
                apply_html_style_animation(scene, cards)

                # 等待展示
                scene.wait(duration_per_card)

                # 如果不是最后一屏，清除当前卡片
                if screen_idx < total_screens - 1:
                    scene.play(*[FadeOut(card) for card in cards], run_time=1.0)

            # 保存当前对象
            scene.current_mobj = VGroup(title_text, *cards)

    except Exception as e:
        logger.error(f"创建HTML风格QA卡片动画时出错: {e}")
        # 创建错误提示
        error_text = Text(f"QA卡片创建失败\n错误: {str(e)[:50]}...", font_size=24, color=RED, font="Arial").move_to(
            ORIGIN
        )
        scene.add(error_text)
        scene.current_mobj = error_text


def create_html_style_card(question: str, answer: str, index: int, total: int) -> VGroup:
    """创建HTML风格的QA卡片，完全复刻设计细节"""

    # HTML中定义的三种颜色主题
    color_themes = [
        "#4A69DD",  # 靛蓝 - card-color-1
        "#1ABC9C",  # 松石绿 - card-color-2
        "#F39C12",  # 活力橙 - card-color-3
    ]

    # A图标的统一灰色
    answer_icon_color = "#4A5568"

    # 根据卡片数量调整尺寸（模拟响应式设计）- 增大尺寸让内容更清晰
    if total == 1:
        card_width, card_height = 8, 6  # 和2个卡片时一样大
    elif total == 2:
        card_width, card_height = 8, 6
    else:  # 3个
        card_width, card_height = 6.5, 6

    # 获取当前卡片的主题色
    theme_color = color_themes[index % len(color_themes)]

    # 创建卡片背景 - 白色背景，圆角12px
    card_bg = RoundedRectangle(
        corner_radius=0.2,  # 12px圆角
        width=card_width,
        height=card_height,
        fill_color=WHITE,
        fill_opacity=1.0,
        stroke_color=GRAY,
        stroke_width=0.5,
        stroke_opacity=0.2,
    )

    # 创建顶部彩色强调条 - 5px高度
    top_accent = Rectangle(
        width=card_width,
        height=0.08,  # 5px高度
        fill_color=theme_color,
        fill_opacity=1.0,
        stroke_width=0,
    )
    top_accent.move_to(card_bg.get_top())

    # 创建卡片阴影效果
    shadow = card_bg.copy()
    shadow.set_fill(color=BLACK, opacity=0.05)
    shadow.set_stroke(width=0)
    shadow.shift(DOWN * 0.05 + RIGHT * 0.05)

    # 创建Q图标 - 增大图标尺寸
    q_circle = Circle(
        radius=0.35,  # 增大图标
        fill_color=theme_color,
        fill_opacity=1.0,
        stroke_width=0,
    )
    q_label = Text("Q", font_size=24, color=WHITE, font="Arial", weight=BOLD)  # 增大字体
    q_icon = VGroup(q_circle, q_label)

    # 创建A图标 - 增大图标尺寸
    a_circle = Circle(
        radius=0.35,  # 增大图标
        fill_color=answer_icon_color,
        fill_opacity=1.0,
        stroke_width=0,
    )
    a_label = Text("A", font_size=24, color=WHITE, font="Arial", weight=BOLD)  # 增大字体
    a_icon = VGroup(a_circle, a_label)

    # 智能自动换行函数 - 定义在使用之前
    def smart_wrap_text(
        text: str, max_width: float, font_size: int, card_height: float, is_question: bool = False
    ) -> str:
        """智能自动换行文本，根据卡片宽度和高度优化显示"""

        # 根据字体大小和卡片宽度精确计算每行字符数
        # 中文字符大约是英文字符的2倍宽度
        char_width_ratio = font_size * 0.6  # 字符宽度比例
        chars_per_line = int(max_width / char_width_ratio * 2)  # 更精确的估算

        # 根据内容类型设置最大行数限制
        if is_question:
            # 问题最多显示2行，保持简洁
            max_lines = 2
        else:
            # 答案文字最多显示5行
            max_lines = 5

        # 设置合理的字符数范围
        chars_per_line = max(15, min(chars_per_line, 50))

        # 智能换行：优先在标点符号处换行
        def smart_break(text, width):
            if len(text) <= width:
                return [text]

            lines = []
            remaining = text

            while remaining and len(lines) < max_lines - 1:
                if len(remaining) <= width:
                    lines.append(remaining)
                    break

                # 寻找合适的断点（标点符号）
                break_point = width
                for i in range(width, max(width // 2, 10), -1):
                    if i < len(remaining) and remaining[i] in "，。！？；：、":
                        break_point = i + 1
                        break
                    elif i < len(remaining) and remaining[i] == " ":
                        break_point = i
                        break

                lines.append(remaining[:break_point].strip())
                remaining = remaining[break_point:].strip()

            # 如果还有剩余内容且未超过行数限制
            if remaining and len(lines) < max_lines:
                if len(remaining) <= width * 1.2:  # 允许最后一行稍长
                    lines.append(remaining)
                else:
                    lines.append(remaining[: width - 3] + "...")
            elif remaining:
                # 如果超过行数限制，截断并添加省略号
                if lines:
                    lines[-1] = lines[-1][: width - 3] + "..."

            return lines

        wrapped_lines = smart_break(text, chars_per_line)
        return "\n".join(wrapped_lines)

    # 文字大小调整 - 显著增大字体让内容清晰可见
    if total == 1:
        q_font_size, a_font_size = 32, 24  # 和2个卡片时一样大
        text_width = card_width - 1.5
    elif total == 2:
        q_font_size, a_font_size = 32, 24  # 大幅增大字体
        text_width = card_width - 1.5
    else:
        q_font_size, a_font_size = 28, 22  # 大幅增大字体，3张卡片时也要保证可读性
        text_width = card_width - 1.2

    # 创建问题文本 - 深色，加粗显示，增加描边效果，支持自动换行
    # 问题最多显示2行，保持简洁
    wrapped_question = smart_wrap_text(question, text_width, q_font_size, card_height, is_question=True)

    q_text = Text(
        wrapped_question,
        font_size=q_font_size,
        color="#1A202C",  # 深色文字
        font="Arial",
        weight=BOLD,
        line_spacing=1.2,
    )
    # 添加描边效果让文字更粗
    q_text.set_stroke(color="#1A202C", width=1, opacity=0.8)
    q_text.set_max_width(text_width)

    # 创建答案文本 - 智能自动换行，较柔和的灰色，最多显示5行
    wrapped_answer = smart_wrap_text(answer, text_width, a_font_size, card_height, is_question=False)

    a_text = Text(
        wrapped_answer,
        font_size=a_font_size,
        color="#4A5568",  # 柔和灰色
        font="Arial",
        line_spacing=1.6,  # 增加行间距提高可读性
    )
    a_text.set_max_width(text_width)

    # 排列问答内容 - 模拟HTML中的gap: 15px
    q_group = VGroup(q_icon, q_text).arrange(RIGHT, buff=0.4, aligned_edge=UP)  # 增大间距
    a_group = VGroup(a_icon, a_text).arrange(RIGHT, buff=0.4, aligned_edge=UP)  # 增大间距

    # 问答对之间的间距 - 模拟HTML中的gap: 25px
    content_group = VGroup(q_group, a_group).arrange(DOWN, buff=0.6, aligned_edge=LEFT)  # 增大间距

    # 内容padding - 模拟HTML中的padding: 30px
    content_group.move_to(card_bg.get_center())

    # 创建悬停效果（可选的装饰性元素）
    hover_glow = card_bg.copy()
    hover_glow.set_fill(opacity=0)
    hover_glow.set_stroke(color=theme_color, width=1, opacity=0.3)

    # 组合所有元素
    card = VGroup(shadow, card_bg, top_accent, hover_glow, content_group)

    return card


def apply_html_style_animation(scene: FeynmanScene, cards: list[VGroup]) -> None:
    """应用现代CSS风格的入场动画"""
    # 模拟CSS的transform: translateY(-8px)和box-shadow变化
    for i, card in enumerate(cards):
        # 初始位置稍微向下
        card.shift(DOWN * 0.5 + LEFT * 2)
        card.set_opacity(0)

        # 优雅的滑入和渐显效果
        scene.play(
            card.animate.shift(UP * 0.5 + RIGHT * 2).set_opacity(1),
            run_time=0.8 + i * 0.2,  # 错开动画时间
            rate_func=smooth,
        )

        # 轻微的悬停效果模拟
        scene.play(card.animate.shift(UP * 0.05), run_time=0.3, rate_func=there_and_back)
