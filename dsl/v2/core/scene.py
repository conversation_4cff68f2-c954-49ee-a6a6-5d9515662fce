"""
FeynmanScene class for Manim animations with region management.
"""

from loguru import logger
from manim import *
from manim_voiceover import VoiceoverScene
import random
import json
import os
from pathlib import Path
from typing import Optional, Dict, Any

from dsl.v2.themes.theme_utils import ThemeUtils
from utils.edgetts_service import EdgeTTSService
from utils.feynman_scene import add_wrapped_subcaption
from dsl.v2.core.transition_effects import TransitionManager

VoiceoverScene.add_wrapped_subcaption = add_wrapped_subcaption


class FeynmanScene(VoiceoverScene):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = logger  # 使 logger 在场景实例中可用
        self.set_speech_service(EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"), create_subcaption=True)
        self.current_mobj: Mobject | None = None
        self.full_screen_rect = Rectangle(height=config.frame_height, width=config.frame_width)

        # 转场相关属性
        self.transition_enabled = True  # 是否启用转场效果
        self.default_transition_type = None  # 默认转场类型，None表示随机选择
        self.transition_run_time = 1.0  # 转场动画时长

        # 场景状态序列化相关
        self.scene_states_dir = Path("temp/scene_states")
        self.scene_states_dir.mkdir(parents=True, exist_ok=True)
        self.current_scene_id = None

    def clear_current_mobj(self, transition_type: Optional[str] = None,
                          run_time: Optional[float] = None):
        """
        清除当前场景中的对象，使用精美的转场效果

        Args:
            transition_type: 转场效果类型，None表示随机选择
            run_time: 转场动画时长，None使用默认值
        """
        if self.current_mobj:
            if not self.transition_enabled:
                # 如果禁用转场，使用原来的简单淡出
                self.play(FadeOut(self.current_mobj))
            else:
                # 使用转场效果
                actual_transition_type = transition_type or self.default_transition_type
                actual_run_time = run_time or self.transition_run_time

                TransitionManager.apply_transition(
                    scene=self,
                    old_mobj=self.current_mobj,
                    new_mobj=None,
                    transition_type=actual_transition_type,
                    run_time=actual_run_time
                )

            self.remove(self.current_mobj)
            self.current_mobj = None

    def save_scene_state(self, scene_id: str, state_type: str = "end"):
        """
        保存场景状态到文件，用于分镜间转场

        Args:
            scene_id: 场景唯一标识符
            state_type: 状态类型，'start' 或 'end'
        """
        if not self.current_mobj:
            return

        try:
            state_data = {
                'scene_id': scene_id,
                'state_type': state_type,
                'mobj_type': type(self.current_mobj).__name__,
                'position': [float(x) for x in self.current_mobj.get_center()],
                'has_content': True,
                'timestamp': str(Path().cwd())  # 简单的时间戳替代
            }

            # 尝试获取更多对象信息
            try:
                if hasattr(self.current_mobj, 'get_bounding_box'):
                    bbox = self.current_mobj.get_bounding_box()
                    state_data['bounding_box'] = {
                        'width': float(bbox[1][0] - bbox[0][0]),
                        'height': float(bbox[1][1] - bbox[0][1])
                    }
                elif hasattr(self.current_mobj, 'width') and hasattr(self.current_mobj, 'height'):
                    state_data['bounding_box'] = {
                        'width': float(self.current_mobj.width),
                        'height': float(self.current_mobj.height)
                    }
                else:
                    # 使用默认尺寸
                    state_data['bounding_box'] = {
                        'width': 2.0,
                        'height': 1.0
                    }
            except Exception as e:
                logger.warning(f"获取对象尺寸失败: {e}")
                state_data['bounding_box'] = {
                    'width': 2.0,
                    'height': 1.0
                }

            # 保存到文件
            state_file = self.scene_states_dir / f"{scene_id}_{state_type}.json"
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)

            logger.info(f"场景状态已保存: {state_file}")

        except Exception as e:
            logger.warning(f"保存场景状态失败: {e}")

    def load_scene_state(self, scene_id: str, state_type: str = "end") -> Optional[Dict[str, Any]]:
        """
        从文件加载场景状态

        Args:
            scene_id: 场景唯一标识符
            state_type: 状态类型，'start' 或 'end'

        Returns:
            场景状态数据，如果文件不存在返回None
        """
        try:
            state_file = self.scene_states_dir / f"{scene_id}_{state_type}.json"
            if state_file.exists():
                with open(state_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载场景状态失败: {e}")
        return None

    def create_hyperbolic_network_background(self):
        # 获取主题颜色
        primary_color = ThemeUtils.get_color("primary")
        background_color = ThemeUtils.get_color("background")

        # Main background - using config.frame_width and config.frame_height
        gradient = Rectangle(
            width=config.frame_width, height=config.frame_height, fill_opacity=1, stroke_width=0
        ).set_color_by_gradient([primary_color, background_color, primary_color])

        # Create hyperbolic network with regular pattern
        network = VGroup()

        # Parameters for the hyperbolic grid
        num_radial_lines = 16
        num_circles = 8
        max_radius = 10

        # Create radial lines
        for i in range(num_radial_lines):
            angle = i * TAU / num_radial_lines
            line = Line(
                ORIGIN,
                max_radius * np.array([np.cos(angle), np.sin(angle), 0]),
                stroke_width=1.2,
                stroke_opacity=0.3,
                stroke_color=primary_color,
            )
            network.add(line)

        # Create concentric circles
        for i in range(1, num_circles):
            radius = i * max_radius / num_circles
            circle = Circle(radius=radius, stroke_width=1.2, stroke_opacity=0.3, stroke_color=primary_color)
            network.add(circle)

        # Create hyperbolic curves connecting points
        for i in range(num_radial_lines):
            for j in range(i + 2, num_radial_lines, 3):  # Connect to every third line
                angle1 = i * TAU / num_radial_lines
                angle2 = j * TAU / num_radial_lines

                # Get points on different circles for a more interesting pattern
                radius1 = (i % 3 + 2) * max_radius / num_circles
                radius2 = (j % 3 + 2) * max_radius / num_circles

                start = radius1 * np.array([np.cos(angle1), np.sin(angle1), 0])
                end = radius2 * np.array([np.cos(angle2), np.sin(angle2), 0])

                # Create a curved path between points
                control = (
                    np.array([(start[0] + end[0]) * 0.5, (start[1] + end[1]) * 0.5, 0]) * 0.5
                )  # Pull control point toward center for hyperbolic effect

                curve = CubicBezier(
                    start,
                    start * 0.6 + control * 0.4,
                    end * 0.6 + control * 0.4,
                    end,
                    stroke_width=0.8,
                    stroke_opacity=0.2,
                    stroke_color=primary_color,
                )
                network.add(curve)

        # Scale the network to fit the screen
        network.scale(0.9)

        # Add a central node
        central_node = Circle(
            radius=0.15, fill_opacity=0.5, stroke_width=1.5, stroke_color=primary_color, fill_color=primary_color
        )

        # Add some smaller nodes at intersection points
        nodes = VGroup()
        for i in range(1, num_circles, 2):
            for j in range(0, num_radial_lines, 4):
                angle = j * TAU / num_radial_lines
                radius = i * max_radius / num_circles
                position = radius * np.array([np.cos(angle), np.sin(angle), 0])

                node = Circle(
                    radius=0.08, fill_opacity=0.4, stroke_width=1, stroke_color=primary_color, fill_color=primary_color
                ).move_to(position)
                nodes.add(node)

        network.add(central_node, nodes)

        # Create a clear space in the center for content
        # Use a solid white background for center to ensure text is clear
        center_mask = Circle(
            radius=5.5,
            fill_opacity=1.0,  # Fully opaque
            stroke_width=0,
            fill_color=background_color,
        )

        return gradient, network, center_mask

    def add_background(self):
        # Create background elements
        gradient, network, center_mask = self.create_hyperbolic_network_background()

        # Add the background to the scene
        self.add(gradient, center_mask)

        # Add the network and start rotation animation in the background
        self.add(network)

        # Create rotation updater function
        def rotate_network(mob, dt):
            mob.rotate(dt * 0.1)  # Rotate at a speed of 0.1 radians per dt

        # Add the continuous rotation updater
        network.add_updater(rotate_network)
        self.add(network)
