"""
分镜间转场功能 - 基于序列化的场景状态生成转场视频
"""

from manim import *
from pathlib import Path
import pickle
import json
from typing import Optional, Dict, Any
from loguru import logger
from dsl.v2.core.transition_effects import TransitionManager
from dsl.v2.core.scene import FeynmanScene


class InterSceneTransition:
    """分镜间转场类，负责生成分镜之间的转场视频"""

    def __init__(self, states_dir: str = "temp/scene_states"):
        self.states_dir = Path(states_dir)
        self.states_dir.mkdir(parents=True, exist_ok=True)

    def load_scene_state(self, scene_id: str) -> Optional[Dict[str, Any]]:
        """加载场景状态"""
        try:
            # 首先尝试加载pickle文件
            state_file = self.states_dir / f"{scene_id}.pkl"
            if state_file.exists():
                with open(state_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.warning(f"加载场景状态失败: {e}")
        return None

    def create_placeholder_mobject(self, state_data: Dict[str, Any]) -> Mobject:
        """根据状态数据创建占位符对象"""
        try:
            # 如果有完整的Mobject对象，直接使用
            if 'mobj' in state_data and state_data['mobj'] is not None:
                try:
                    # 尝试复制原始对象
                    original_mobj = state_data['mobj']
                    return original_mobj.copy()
                except Exception as e:
                    logger.warning(f"复制原始对象失败: {e}")

            # 否则根据对象类型创建占位符
            mobj_type = state_data.get('mobj_type', 'VGroup')
            position = state_data.get('position', [0, 0, 0])

            if 'bounding_box' in state_data:
                bbox = state_data['bounding_box']
                width = bbox.get('width', 2)
                height = bbox.get('height', 1)

                # 创建一个矩形作为占位符
                placeholder = Rectangle(
                    width=width,
                    height=height,
                    fill_opacity=0.3,
                    fill_color=BLUE,
                    stroke_color=WHITE,
                    stroke_width=2
                )
            else:
                # 默认占位符
                placeholder = Rectangle(
                    width=2,
                    height=1,
                    fill_opacity=0.3,
                    fill_color=BLUE,
                    stroke_color=WHITE,
                    stroke_width=2
                )

            # 设置位置
            placeholder.move_to(position)
            return placeholder

        except Exception as e:
            logger.warning(f"创建占位符对象失败: {e}")
            # 返回默认占位符
            return Rectangle(width=2, height=1, fill_opacity=0.3, fill_color=BLUE)

    def generate_transition_scene(self,
                                from_scene_id: str,
                                to_scene_id: str,
                                transition_type: Optional[str] = None,
                                output_file: str = "transition.py") -> bool:
        """
        生成分镜间转场的Manim场景代码

        Args:
            from_scene_id: 源分镜ID
            to_scene_id: 目标分镜ID
            transition_type: 转场类型，None表示随机选择
            output_file: 输出的Python文件名

        Returns:
            是否成功生成
        """
        try:
            # 加载两个分镜的状态
            from_state = self.load_scene_state(from_scene_id)
            to_state = self.load_scene_state(to_scene_id, "start")

            if not from_state and not to_state:
                logger.warning(f"无法找到分镜状态: {from_scene_id} -> {to_scene_id}")
                return False

            # 选择转场类型
            if not transition_type:
                transition_type = TransitionManager.get_random_transition()

            # 生成Manim代码
            code = self._generate_manim_code(from_state, to_state, transition_type)

            # 写入文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(code)

            logger.info(f"转场场景代码已生成: {output_file}")
            return True

        except Exception as e:
            logger.error(f"生成转场场景失败: {e}")
            return False

    def _generate_manim_code(self,
                           from_state: Optional[Dict[str, Any]],
                           to_state: Optional[Dict[str, Any]],
                           transition_type: str) -> str:
        """生成Manim转场代码"""

        code_lines = [
            "from manim import *",
            "from dsl.v2.core.scene import FeynmanScene",
            "from dsl.v2.core.transition_effects import TransitionManager",
            "",
            "class InterSceneTransitionScene(FeynmanScene):",
            "    def construct(self):",
            "        # 设置背景",
            "        self.camera.background_color = '#1e1e1e'",
            "",
        ]

        # 创建源对象
        if from_state:
            code_lines.extend([
                "        # 创建源场景对象占位符",
                f"        from_placeholder = Rectangle(",
                f"            width={from_state.get('bounding_box', {}).get('width', 2)},",
                f"            height={from_state.get('bounding_box', {}).get('height', 1)},",
                f"            fill_opacity=0.3,",
                f"            fill_color=BLUE,",
                f"            stroke_color=WHITE,",
                f"            stroke_width=2",
                f"        )",
                f"        from_placeholder.move_to({from_state.get('position', [0, 0, 0])})",
                f"        self.add(from_placeholder)",
                "",
            ])
        else:
            code_lines.extend([
                "        from_placeholder = None",
                "",
            ])

        # 创建目标对象
        if to_state:
            code_lines.extend([
                "        # 创建目标场景对象占位符",
                f"        to_placeholder = Rectangle(",
                f"            width={to_state.get('bounding_box', {}).get('width', 2)},",
                f"            height={to_state.get('bounding_box', {}).get('height', 1)},",
                f"            fill_opacity=0.3,",
                f"            fill_color=GREEN,",
                f"            stroke_color=WHITE,",
                f"            stroke_width=2",
                f"        )",
                f"        to_placeholder.move_to({to_state.get('position', [0, 0, 0])})",
                "",
            ])
        else:
            code_lines.extend([
                "        to_placeholder = None",
                "",
            ])

        # 应用转场效果
        code_lines.extend([
            "        # 应用转场效果",
            f"        if from_placeholder:",
            f"            TransitionManager.apply_transition(",
            f"                scene=self,",
            f"                old_mobj=from_placeholder,",
            f"                new_mobj=to_placeholder,",
            f"                transition_type='{transition_type}',",
            f"                run_time=1.5",
            f"            )",
            f"        elif to_placeholder:",
            f"            self.play(FadeIn(to_placeholder), run_time=1.5)",
            "",
            "        self.wait(0.5)",
        ])

        return "\n".join(code_lines)


def generate_inter_scene_transition(from_scene_id: str,
                                  to_scene_id: str,
                                  transition_type: Optional[str] = None,
                                  output_file: str = "transition.py") -> bool:
    """
    便捷函数：生成分镜间转场

    Args:
        from_scene_id: 源分镜ID
        to_scene_id: 目标分镜ID
        transition_type: 转场类型
        output_file: 输出文件名

    Returns:
        是否成功生成
    """
    transition = InterSceneTransition()
    return transition.generate_transition_scene(
        from_scene_id, to_scene_id, transition_type, output_file
    )
