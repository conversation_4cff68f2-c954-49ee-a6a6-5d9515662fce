# animate_emoji_flowchart

## 效果

创建一个交互式emoji流程图动画，从文本中自动检测emoji并生成对应的流程图。
支持多种布局和动画风格，自动调整大小以适应屏幕。


## 使用场景

- 展示工作流程或业务流程
- 可视化步骤序列或决策树
- 创建教学演示的流程图
- 显示系统架构或数据流

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content | str | 包含emoji和描述的流程文本 | 是 | - |
| layout | str | 布局样式。可选值：horizontal, vertical, circular, grid | 否 | horizontal |
| connection_style | str | 连接线样式。可选值：arrow, line, curve | 否 | arrow |
| animation_style | str | 动画风格。可选值：sequence, simultaneous, cascade | 否 | sequence |
| max_count | int | 最大emoji数量限制 | 否 | 8 |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_emoji_flowchart",
  "params": {
    "content": "用户打开应用 📱 → 浏览商品目录 📚 → 选择商品 🛒 → \n添加到购物车 ➕ → 结算支付 💳 → 订单确认 ✅\n",
    "layout": "horizontal",
    "connection_style": "arrow",
    "animation_style": "sequence",
    "narration": "这是一个电商购物流程的演示"
  }
}
```

### 示例 2

```json
{
  "type": "animate_emoji_flowchart",
  "params": {
    "content": "需求分析 📋 收集用户需求\n设计阶段 ✏️ 制作原型设计  \n开发实现 💻 编写代码\n测试验证 🔍 质量保证\n部署上线 🚀 发布产品\n",
    "layout": "circular",
    "connection_style": "curve",
    "animation_style": "cascade",
    "max_count": 6,
    "narration": "软件开发生命周期的完整流程展示"
  }
}
```

## 注意事项

- 自动从文本中检测emoji并提取对应的文本标签
- 布局算法会自动防止重叠并适应屏幕尺寸
- 连接线会智能避开emoji边界
- 支持限制emoji数量以优化布局效果

