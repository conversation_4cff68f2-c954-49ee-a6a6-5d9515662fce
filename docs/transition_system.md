# 转场效果系统文档

## 概述

转场效果系统为动画函数之间提供了精美的转场效果，替代了原来简单的淡入淡出机制。系统支持同一分镜内的转场和分镜间的转场。

## 功能特性

### 1. 同一分镜内转场
- 自动应用转场效果
- 支持多种转场类型
- 可配置转场参数
- 随机转场选择

### 2. 分镜间转场
- 场景状态序列化
- 独立转场视频生成
- 支持视频拼接
- 基于状态数据的转场

### 3. 转场效果库
- 11种经典转场效果
- 可扩展的转场系统
- 统一的转场接口

## 支持的转场类型

| 转场类型 | 描述 | 效果 |
|---------|------|------|
| `fade` | 淡入淡出 | 经典的渐变效果 |
| `slide_left` | 向左滑动 | 内容向左滑出，新内容从右侧滑入 |
| `slide_right` | 向右滑动 | 内容向右滑出，新内容从左侧滑入 |
| `slide_up` | 向上滑动 | 内容向上滑出，新内容从下方滑入 |
| `slide_down` | 向下滑动 | 内容向下滑出，新内容从上方滑入 |
| `zoom` | 缩放转场 | 先缩小消失，再放大出现 |
| `rotate` | 旋转转场 | 旋转切换效果 |
| `wipe_left` | 向左擦除 | 从左到右擦除效果 |
| `circle_wipe` | 圆形擦除 | 圆形缩放擦除 |
| `flip` | 水平翻转 | 水平翻转切换 |
| `spiral` | 螺旋转场 | 螺旋消失和出现 |

## 系统架构

### 核心组件

1. **FeynmanScene** (`dsl/v2/core/scene.py`)
   - 增强的 `clear_current_mobj()` 方法
   - 场景状态序列化功能
   - 转场配置属性

2. **TransitionEffects** (`dsl/v2/core/transition_effects.py`)
   - 转场效果实现
   - TransitionManager 管理器
   - 统一的转场接口

3. **InterSceneTransition** (`dsl/v2/core/inter_scene_transition.py`)
   - 分镜间转场生成
   - 状态数据处理
   - Manim代码生成

### 工作流程

#### 同一分镜内转场
1. 动画函数调用 `scene.clear_current_mobj()`
2. 系统检查是否启用转场
3. 选择转场类型（配置或随机）
4. 应用转场效果
5. 清除旧对象，设置新对象

#### 分镜间转场
1. 动画函数保存开始和结束状态
2. 状态数据序列化到JSON文件
3. 转场生成器读取状态数据
4. 生成独立的转场Manim代码
5. 渲染转场视频用于拼接

## 使用方法

### 1. 基本配置

```python
from dsl.v2.core.scene import FeynmanScene

class MyScene(FeynmanScene):
    def construct(self):
        # 启用转场效果
        self.transition_enabled = True
        
        # 设置默认转场类型（None表示随机选择）
        self.default_transition_type = "slide_left"
        
        # 设置转场动画时长
        self.transition_run_time = 1.5
```

### 2. 自动转场

动画函数会自动应用转场效果：

```python
# 第一个内容
animate_markdown(
    scene=self,
    content="# 第一个内容",
    id="content1"
)

# 第二个内容 - 会自动应用转场
animate_markdown(
    scene=self,
    content="# 第二个内容", 
    id="content2"
)
```

### 3. 手动转场

```python
from dsl.v2.core.transition_effects import TransitionManager

# 手动应用转场
TransitionManager.apply_transition(
    scene=self,
    old_mobj=old_content,
    new_mobj=new_content,
    transition_type="zoom",
    run_time=2.0
)
```

### 4. 分镜间转场

```python
from dsl.v2.core.inter_scene_transition import generate_inter_scene_transition

# 生成分镜间转场
success = generate_inter_scene_transition(
    from_scene_id="scene_a_end",
    to_scene_id="scene_b_start",
    transition_type="slide_left",
    output_file="transition_a_to_b.py"
)

if success:
    # 渲染转场视频
    os.system("manim transition_a_to_b.py InterSceneTransitionScene -ql")
```

## 配置选项

### FeynmanScene 属性

- `transition_enabled`: 是否启用转场效果（默认: True）
- `default_transition_type`: 默认转场类型（None表示随机选择）
- `transition_run_time`: 转场动画时长（默认: 1.0秒）
- `scene_states_dir`: 状态文件保存目录（默认: "temp/scene_states"）

### 转场参数

- `transition_type`: 转场类型名称
- `run_time`: 转场动画时长
- `old_mobj`: 源对象
- `new_mobj`: 目标对象（可选）

## 文件结构

```
dsl/v2/core/
├── scene.py                    # 增强的FeynmanScene
├── transition_effects.py      # 转场效果库
└── inter_scene_transition.py  # 分镜间转场

temp/scene_states/              # 状态文件目录
├── content1_start.json
├── content1_end.json
└── ...

docs/
└── transition_system.md        # 本文档

测试文件:
├── test_transition_simple.py   # 简化测试
├── demo_transition_usage.py    # 使用演示
└── test_transition_system.py   # 完整测试
```

## 测试和演示

### 运行测试

```bash
# 简化版测试（推荐）
python test_transition_simple.py

# 使用演示
python demo_transition_usage.py

# 完整测试（需要SoX）
python test_transition_system.py
```

### 查看结果

生成的视频文件位于：
- `media/videos/test_transition_simple/480p15/`
- `media/videos/demo_transition_usage/480p15/`

## 扩展转场效果

### 添加新转场

1. 在 `TransitionEffects` 类中添加静态方法：

```python
@staticmethod
def my_custom_transition(old_mobj: Mobject, new_mobj: Optional[Mobject] = None, 
                        run_time: float = 1.0) -> List[Animation]:
    """自定义转场效果"""
    animations = [old_mobj.animate.scale(0).rotate(PI)]
    if new_mobj:
        new_mobj.scale(0).rotate(-PI)
        animations.append(new_mobj.animate.scale(1).rotate(PI))
    return animations
```

2. 在 `TRANSITION_EFFECTS` 字典中注册：

```python
TRANSITION_EFFECTS = {
    # ... 现有转场
    'my_custom': TransitionEffects.my_custom_transition,
}
```

## 注意事项

1. **语音功能**: 当前测试禁用了语音功能，避免SoX依赖问题
2. **状态保存**: 某些Mobject可能没有 `bounding_box` 属性，系统会使用默认值
3. **性能**: 复杂转场可能影响渲染性能，建议适度使用
4. **兼容性**: 转场系统与现有动画函数完全兼容

## 未来改进

1. 添加更多转场效果
2. 支持转场效果组合
3. 优化状态序列化
4. 添加转场预览功能
5. 支持自定义转场参数
