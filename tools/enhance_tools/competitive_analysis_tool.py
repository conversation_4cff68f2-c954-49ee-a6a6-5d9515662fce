#!/usr/bin/env python3
"""
智能对比分析工具 - 多维度深度洞察
根据内容自动识别类型，搜索相关替代方案，进行智能化对比分析
支持产品、技术、算法、书籍、工具等多种类型的对比分析
"""

import json
import os
import re
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class CompetitiveAnalysisTool(EnhancementTool):
    """智能对比分析工具 - 多维度深度洞察"""

    tool_name = "competitive_analysis"
    tool_description = "智能对比分析：搜索并分析相关替代方案，提供多维度深度对比洞察"
    tool_category = ToolCategory.DEEP_INSIGHTS

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        self.search_enabled = False
        if config:
            self._init_model()
            self._init_search()

    def _init_model(self):
        """初始化Camel模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType

            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )

            system_prompt = """你是专业的技术与学术对比分析专家，擅长识别、搜索和对比分析相关技术、算法、模型、工具和学术方法。

你的专业领域包括：
- 机器学习和深度学习算法、模型架构
- 技术框架、开发工具、编程语言
- 学术研究方法、实验设计、评估指标
- 数据集、基准测试、性能评估
- 开源项目、软件工具、API服务

你的任务是：
1. 准确识别内容中的核心技术、算法、模型或学术方法
2. 判断是否存在有价值的技术对比空间
3. 生成精准的搜索关键词，涵盖学术和技术领域
4. 基于搜索结果进行深度技术对比分析
5. 提供客观、有洞察力的差异化技术分析，帮助读者理解技术选择

请严格按照要求的JSON格式输出，确保分析的准确性和实用性。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def _init_search(self):
        """初始化搜索功能"""
        try:
            # 这里可以集成多种搜索API，比如Google Search API, Bing Search API等
            # 暂时使用简单的网络搜索模拟
            self.search_enabled = True
            logger.info("搜索功能初始化成功")
        except Exception as e:
            logger.warning(f"搜索功能初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """智能对比分析工具 - 搜索并分析相关替代方案，提供多维度深度对比洞察

**核心作用**：
智能对比分析是一种增值服务工具，通过自动识别内容中的产品、技术、算法、研究方法或学术理论，主动搜索相关替代选择或竞争方案，并进行多维度对比分析，为观众提供更全面的选择视角和决策参考，即使原内容没有明确提及对比需求。

**适合的内容类型**：
- **学术研究内容**（如论文中的算法、模型、理论框架、实验方法、数据集、评估指标）
- **技术方案和架构**（如算法介绍、技术栈选择、设计模式、协议标准、系统架构）
- **产品和工具介绍**（如软件工具、开发框架、硬件设备、在线服务、API接口）
- **研究方法和流程**（如机器学习方法、数据处理流程、实验设计、优化策略）
- **开源项目展示**（如GitHub项目、开发库、工具集、框架介绍、模型实现）
- **学习资源推荐**（如书籍介绍、课程推荐、教程分享、学习平台）
- **商业产品分析**（如SaaS服务、移动应用、企业解决方案）

**典型应用场景**：
✅ 论文介绍Transformer架构 → 对比LSTM、GRU、CNN等序列建模方法
✅ 研究提出新的优化算法 → 与Adam、SGD、RMSprop等优化器对比
✅ 论文使用特定数据集 → 对比ImageNet、COCO、BERT等相关数据集
✅ 介绍某种评估指标 → 与BLEU、ROUGE、F1-Score等指标对比
✅ 提出新的神经网络结构 → 与ResNet、DenseNet、EfficientNet等架构对比
✅ 介绍强化学习方法 → 与Q-Learning、A3C、PPO等算法对比
✅ 论文采用特定实验设置 → 与标准实验协议和基准测试对比
✅ 展示某个开源项目 → 对比GitHub上的类似项目和替代方案
✅ 介绍某种损失函数 → 与交叉熵、MSE、Focal Loss等损失函数对比
✅ 推荐一本技术书籍 → 分析与其他相关书籍的差异和适用性

**不适合的内容类型**：
- 纯理论证明和数学推导（如定理证明、公式推导、数学原理）
- 历史叙述和时间发展（如技术发展历程、学科发展史、个人经历）
- 基础概念解释和定义（如什么是机器学习、基本概念介绍）
- 操作指南和教程步骤（如安装步骤、配置教程、使用手册）
- 个人观点和主观体验（如学习心得、使用感受、个人见解）
- 新闻资讯和事件报道（如行业动态、会议报告、新闻摘要）
- 抽象哲学和思想讨论（如研究哲学、认知理论、抽象思维）

**典型不适用场景**：
❌ 纯粹解释深度学习的基本概念和数学原理
❌ 讲述某个研究领域的历史发展脉络和重要里程碑
❌ 提供深度学习框架的详细安装和环境配置步骤
❌ 分享个人的研究经验和学术心得体会
❌ 报道某个学术会议的最新动态和趋势概述
❌ 进行复杂的数学公式推导和理论证明过程

**判断标准**：
内容是否涉及具体的、可识别的技术方法、算法模型、实验工具、研究框架或学术资源？该对象是否在学术界或工业界存在替代选择或竞争方案？为观众提供对比信息是否有助于更好地理解该对象的定位、优势和适用场景？如果答案是肯定的，则适合使用智能对比分析工具。

**关键识别要点**：
- 重点关注内容中的"具体技术对象"而非"抽象概念描述"
- 优先识别算法、模型、框架、方法、工具、数据集等具有竞争性的技术对象
- 即使是论文中提到的某个技术细节，如果存在明显的替代方案，对比分析仍有价值
- 对于新兴技术或小众方法，对比分析更有助于观众理解其在技术谱系中的定位
- 关注可量化、可比较的技术特征（如性能指标、复杂度、适用范围等）

**输出形式**：生成结构化的对比分析报告，包含替代方案识别、多维度技术对比、差异化分析和选择建议，为观众提供更全面的技术认知视角和决策参考。"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 500  # 对比分析需要足够的内容来提取关键信息
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
            and self.agent is not None
            and self.search_enabled
        )

        return basic_check

    def apply_tool(
        self, content: str, output_dir: str, context: dict[str, Any], focus: str = None
    ) -> Optional[str]:
        """应用竞品对比分析工具"""
        logger.info(f"🔧 应用{self.tool_name}工具，准备进行竞品对比分析")

        try:
            os.makedirs(output_dir, exist_ok=True)

            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"竞品对比分析结果已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行竞品对比分析，传递focus参数
            result_data = self._process_competitive_analysis(content, context, focus)

            if not result_data:
                return None

            # 将结果转换为JSON字符串
            result_json = json.dumps(result_data, ensure_ascii=False, indent=2)

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_json)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            # 直接返回JSON字符串
            return result_json

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_competitive_analysis(self, content: str, context: dict[str, Any], focus: str = None) -> Optional[dict[str, Any]]:
        """执行竞品对比分析的核心逻辑"""
        try:
            # 第一步：分析内容并生成搜索关键词
            keywords_data = self._extract_search_keywords(content, context, focus)
            if not keywords_data:
                return None

            # 第二步：搜索竞品信息
            search_results = self._search_competitors(keywords_data.get("search_keywords", []))

            # 第三步：生成对比分析报告
            analysis_result = self._generate_competitive_analysis(content, context, keywords_data, search_results, focus)

            return analysis_result

        except Exception as e:
            logger.error(f"竞品对比分析处理失败: {e}")
            return None

    def _extract_search_keywords(self, content: str, context: dict[str, Any], focus: str = None) -> Optional[dict[str, Any]]:
        """提取搜索关键词和产品信息"""
        purpose = context.get("purpose", "对比分析")

        # 构建focus指令
        focus_instruction = ""
        if focus:
            focus_instruction = f"""
**重点关注方向**：{focus}
请在分析时特别关注这个方向，确保相关内容在对比分析中得到充分体现和突出展示。"""

        prompt = f"""基于以下内容，智能识别内容类型并提取关键信息用于对比分析：

**内容材料**：
{content[:6000]}{'...(内容截断)' if len(content) > 6000 else ''}

**分析目标**：{purpose}

{focus_instruction}

**智能识别要求**：
1. 自动识别内容的主要类型（如：软件工具、算法、书籍、技术方案、开源项目等）
2. 根据内容类型自动生成最适合的对比维度
3. 精准提取核心特征和关键词
4. 生成有效的搜索策略
5. 严格控制字数，保持信息精简

请分析内容并严格按照JSON格式输出：

{{
    "content_type": "内容类型（如：开源工具、算法、书籍、技术框架、编程语言等，限制15字内）",
    "target_subject": {{
        "name": "主要对象名称（产品/算法/书籍/工具等，限制20字内）",
        "category": "所属类别（限制10字内）",
        "key_characteristics": ["核心特征1（限制15字）", "核心特征2（限制15字）", "核心特征3（限制15字）"],
        "application_areas": ["应用领域1（限制15字）", "应用领域2（限制15字）"]
    }},
    "search_keywords": [
        "主要名称 + competitors",
        "主要名称 + alternatives", 
        "类别名称 + comparison",
        "主要名称 + vs",
        "类别名称 + best"
    ],
    "comparison_dimensions": [
        "维度1（如功能特性、算法复杂度、学习难度等，限制10字内）",
        "维度2（如性能表现、实用性、适用场景等，限制10字内）", 
        "维度3（如社区支持、成本、可扩展性等，限制10字内）",
        "维度4（根据内容类型自动确定，限制10字内）"
    ],
    "analysis_focus": "对比分析的重点关注领域（限制30字内）",
    "search_strategy": "搜索策略说明（如何找到最佳对比对象，限制40字内）"
}}

**注意**：
- 对于git项目：重点关注功能、性能、社区活跃度、文档质量等
- 对于算法：重点关注时间复杂度、空间复杂度、适用场景、实现难度等  
- 对于书籍：重点关注内容深度、适用人群、实用性、评价等
- 对于技术方案：重点关注可行性、成本、维护性、扩展性等
- 请根据实际内容类型灵活调整对比维度
- 所有文本必须精简，严格遵守字数限制"""

        try:
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content

            # 提取JSON
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = response_content.strip()

            result_data = json.loads(json_str)
            logger.info(f"搜索关键词提取成功，识别内容类型：{result_data.get('content_type', '未知')}")
            return result_data

        except Exception as e:
            logger.error(f"搜索关键词提取失败: {e}")
            return None

    def _search_competitors(self, keywords: list[str]) -> list[dict[str, Any]]:
        """搜索竞品信息"""
        search_results = []

        try:
            # 模拟搜索结果 - 实际实现中应该调用真实的搜索API
            for keyword in keywords[:3]:  # 限制搜索数量
                # 这里应该调用实际的搜索API，比如Google Search API
                # 暂时使用模拟数据
                mock_result = {
                    "keyword": keyword,
                    "results": [
                        {
                            "title": f"关于{keyword}的搜索结果1",
                            "url": "https://example.com/1",
                            "snippet": f"这是关于{keyword}的详细介绍和对比分析内容...",
                        },
                        {
                            "title": f"关于{keyword}的搜索结果2",
                            "url": "https://example.com/2",
                            "snippet": f"另一个关于{keyword}的竞品分析和评测内容...",
                        },
                    ],
                }
                search_results.append(mock_result)

            logger.info(f"搜索完成，获得{len(search_results)}组结果")
            return search_results

        except Exception as e:
            logger.error(f"竞品搜索失败: {e}")
            return []

    def _generate_competitive_analysis(
        self, content: str, context: dict[str, Any], keywords_data: dict[str, Any], search_results: list[dict[str, Any]], focus: str = None
    ) -> Optional[dict[str, Any]]:
        """生成竞品对比分析报告"""
        purpose = context.get("purpose", "对比分析")

        # 构建focus指令
        focus_instruction = ""
        if focus:
            focus_instruction = f"""
**重点关注方向**：{focus}
请在生成对比分析时特别关注这个方向，确保相关内容在分析报告中得到充分体现和突出展示。"""

        # 构建搜索结果摘要
        search_summary = ""
        for result in search_results:
            search_summary += f"搜索关键词: {result['keyword']}\n"
            for item in result.get("results", [])[:2]:
                search_summary += f"- {item['title']}: {item['snippet'][:200]}...\n"
            search_summary += "\n"

        content_type = keywords_data.get("content_type", "产品/技术")
        target_subject = keywords_data.get("target_subject", {})
        comparison_dimensions = keywords_data.get("comparison_dimensions", [])

        prompt = f"""基于原始内容和搜索到的信息，生成智能对比分析报告：

**原始内容**：
{content[:4000]}{'...(内容截断)' if len(content) > 4000 else ''}

**内容类型**：{content_type}

**分析目标**：
{json.dumps(target_subject, ensure_ascii=False, indent=2)}

**预设对比维度**：
{json.dumps(comparison_dimensions, ensure_ascii=False, indent=2)}

**搜索到的相关信息**：
{search_summary[:3000]}{'...(内容截断)' if len(search_summary) > 3000 else ''}

**分析目标**：{purpose}

{focus_instruction}

**智能分析要求**：
1. 根据内容类型调整对比分析结构
2. 使用最适合的对比维度和指标
3. 提供客观、有价值的对比洞察
4. 内容精炼，重点突出差异化价值
5. 严格控制字数：每个字段限制在50字以内，避免冗余信息
6. 替代方案数量控制在3-5个最有代表性的
7. 避免空洞的形容词，使用具体的对比数据

请生成结构化的对比分析报告，严格按照以下JSON格式输出：

{{
    "内容类型": "{content_type}",
    "主要对象": {{
        "名称": "目标对象名称（限制20字内）",
        "类别": "所属类别（限制10字内）",
        "核心特点": "核心特点概述（限制50字内）"
    }},
    "主要替代方案": [
        {{
            "名称": "替代方案1（限制20字内）",
            "类别": "类别（限制10字内）",
            "定位": "市场定位或特点（限制30字内）",
            "优势": "主要优势（限制50字内）",
            "劣势": "主要劣势（限制50字内）",
            "适用场景": "适用场景（限制40字内）"
        }}
    ],
    "维度对比分析": {{
        "{comparison_dimensions[0] if comparison_dimensions else '功能特性'}": "第一维度对比分析（限制50字内）",
        "{comparison_dimensions[1] if len(comparison_dimensions) > 1 else '性能表现'}": "第二维度对比分析（限制50字内）",
        "{comparison_dimensions[2] if len(comparison_dimensions) > 2 else '易用性'}": "第三维度对比分析（限制50字内）",
        "{comparison_dimensions[3] if len(comparison_dimensions) > 3 else '生态支持'}": "第四维度对比分析（限制50字内）"
    }},
    "差异化分析": {{
        "独特优势": "相对其他方案的独特优势（限制50字内）",
        "改进空间": "可改进的方面（限制50字内）",
        "适用建议": "使用建议和适用场景（限制50字内）",
        "选择标准": "如何在这些方案中做选择（限制50字内）"
    }},
    "综合评估": {{
        "推荐指数": "推荐程度（1-5星）",
        "关键结论": "核心结论（限制50字内）",
        "决策建议": "具体的选择建议（限制50字内）"
    }}
}}

**特殊说明**：
- 对于开源项目：重点分析star数、活跃度、社区支持、文档质量等
- 对于算法：重点分析复杂度、精度、适用数据规模、实现难度等
- 对于书籍：重点分析内容深度、适合人群、实用性、权威性等
- 对于工具软件：重点分析功能完整性、易用性、性能、成本等
- 请根据实际内容类型灵活调整分析维度和评判标准
- 所有文本内容必须精简明了，严格遵守字数限制"""

        try:
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content

            # 提取JSON
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = response_content.strip()

            result_data = json.loads(json_str)

            logger.info(f"智能对比分析报告生成成功，类型：{result_data.get('内容类型', '未知')}")
            return result_data  # 直接返回解析后的JSON数据

        except Exception as e:
            logger.error(f"对比分析报告生成失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成智能对比分析的介绍文本"""
        # 兼容新的返回格式：tool_result现在可能是JSON字符串
        if isinstance(tool_result, str):
            try:
                data = json.loads(tool_result)
            except json.JSONDecodeError:
                return ""
        else:
            # 兼容旧格式
            data = tool_result

        if not data:
            return ""

        content_type = data.get("内容类型", "未知类型")
        main_subject = data.get("主要对象", {})
        alternatives = data.get("主要替代方案", [])
        comprehensive_eval = data.get("综合评估", {})
        differentiation = data.get("差异化分析", {})

        intro = f"## 🔍 {self.tool_description}\n\n"

        # 基本信息
        if main_subject:
            intro += f"**分析类型**: {content_type}\n"
            intro += f"**分析对象**: {main_subject.get('名称', 'N/A')}\n"
            intro += f"**所属类别**: {main_subject.get('类别', 'N/A')}\n"
            if main_subject.get('核心特点'):
                intro += f"**核心特点**: {main_subject.get('核心特点')}\n"
            intro += "\n"

        # 替代方案概览
        if alternatives:
            intro += "### 主要替代方案\n"
            for alt in alternatives[:3]:  # 限制显示前3个
                name = alt.get("名称", "N/A")
                positioning = alt.get("定位", "N/A")
                intro += f"- **{name}**: {positioning}\n"
            intro += "\n"

        # 核心洞察
        if differentiation:
            unique_advantages = differentiation.get("独特优势")
            if unique_advantages:
                intro += f"### 核心优势\n{unique_advantages}\n\n"
            
            selection_criteria = differentiation.get("选择标准")
            if selection_criteria:
                intro += f"### 选择建议\n{selection_criteria}\n\n"

        # 综合评估
        if comprehensive_eval:
            recommendation = comprehensive_eval.get("推荐指数")
            key_conclusion = comprehensive_eval.get("关键结论")
            if recommendation:
                intro += f"**推荐指数**: {recommendation}\n"
            if key_conclusion:
                intro += f"**关键结论**: {key_conclusion}\n\n"

        # 结语
        intro += f"提供{content_type}的多维度智能对比分析，助力最优选择。\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = CompetitiveAnalysisTool(config.config)
    
    # 测试内容
    test_content = """
    Quarkdown是一个现代化的文档处理工具，专门用于将Markdown文档转换为高质量的PDF、HTML和其他格式。
    它具有以下特点：
    1. 支持多种输出格式
    2. 高度可定制的样式系统
    3. 内置数学公式支持
    4. 代码高亮功能
    5. 快速的处理速度
    
    Quarkdown使用Rust语言开发，具有出色的性能表现，特别适合处理大型文档项目。
    它提供了简洁的命令行界面，同时也支持配置文件进行高级定制。
    """
    
    # 测试focus参数
    test_focus = "重点关注文档处理工具的性能对比和功能特性分析，特别是与Pandoc、LaTeX等工具的差异"
    
    result = tool.apply_tool(
        content=test_content,
        output_dir="./test_output",
        context={"purpose": "文档处理工具对比分析"},
        focus=test_focus
    )
    
    if result:
        print("竞品对比分析完成！")
        
        # 解析JSON字符串
        try:
            result_data = json.loads(result)
            print(f"结果类型: {result_data.get('内容类型', 'N/A')}")
            print(f"主要对象: {result_data.get('主要对象', {}).get('名称', 'N/A')}")
            
            # 生成介绍文本
            intro = tool.generate_intro(result)
            print("\n生成的介绍文本:")
            print(intro)
            
            # 输出完整的JSON结果
            print("\n完整JSON输出:")
            print(result)
        except json.JSONDecodeError:
            print("JSON解析失败")
    else:
        print("竞品对比分析失败！")
