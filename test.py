# Generated by Feynman DSL v2 Code Generator
# -*- coding: utf-8 -*-

import sys
import os
import pickle
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from manim.utils.hashing import get_json
# from dsl.v2.core.scene import FeynmanScene
# from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class Storyboard_5(Scene):
    def construct(self):
        text = Text("text")
        text.shift(DOWN)
        pickle.dump(text, open("out.pickle", "wb"))
        text2 = pickle.load(open("out.pickle", "rb"))
        self.add(text2)
        self.wait()
