# Generated by Feynman DSL v2 Code Generator
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class Storyboard_5(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 2: animate_step_by_step
        animate_step_by_step(
            scene=self,
            steps=[{'step_number': '1', 'title': '校准集与模型', 'content': '## 校准集构建与模型训练\n- 初始探索构建校准集。\n- 基于校准集训练代理模型g(x)预测奖励。\n- 计算不确定性偏移量α。\n`1-δ = 0.9, α = 0.8`', 'color': '#FF6B6B', 'narration': '首先，MARCO通过初始探索构建校准集，并训练一个预测奖励的代理模型，同时计算出量化预测不确定性的偏移量α。'}, {'step_number': '2', 'title': 'CP阈值', 'content': '## CP过滤阈值τ的确定\n- 设定奖励阈值τ。\n- 预测奖励 + 偏移 ≥ τ才接受。\n`CIFAR-10最佳τ=5.5`', 'color': '#4ECDC4', 'narration': '然后，系统预设一个奖励阈值τ。只有当预测奖励加上不确定性偏移量后仍高于τ的候选架构，才会被进一步评估，否则将被丢弃。'}, {'step_number': '3', 'title': '生成与预测', 'content': '## 候选架构的生成与初始预测\n\n- HCA与QA协同生成候选架构x_a。\n- 代理模型g(x_a)进行奖励预测ˆR_t。\n`C1: 3.0, C2: 4.0, C3: 6.5`', 'color': '#45B7D1', 'narration': '在每个强化学习时间步，两个智能体协同生成新的候选架构，并由预训练的代理模型快速进行奖励预测。'}, {'step_number': '4', 'title': '应用过滤规则', 'content': '## 应用CP过滤规则进行判断\n- 对比 (g(x_a) + α_{1-δ}) 与 τ。\n- 如果小于 τ，则丢弃。\n`C1: 3.8<5.5(丢弃), C2: 4.8<5.5(丢弃), C3: 7.3≥5.5(接受)`', 'color': '#FFD700', 'narration': '接着，根据CP过滤规则，将预测奖励加上偏移量后与设定的阈值进行比较。如果低于阈值，该候选架构将被立即废弃，从而避免了后续昂贵的评估。'}, {'step_number': '5', 'title': '剩余候选的昂贵评估与奖励计算', 'content': '## 剩余候选的昂贵评估与奖励计算\n- 仅通过过滤的候选进入评估。\n- 进行部分训练获取准确度。\n- 硬件模拟获得延迟内存。\n- 计算精确奖励 R_a。\n`C3精度87.0%，延迟9.5ms`', 'color': '#A0D911', 'narration': '最后，只有通过CP过滤的少数有前景的候选架构，才会进入耗时的部分训练和硬件模拟阶段，从而获得精确的奖励值，为智能体的学习提供反馈。'}],
            # steps=[{'step_number': '3', 'title': '生成与预测', 'content': '## 候选架构的生成与初始预测\n\n- HCA与QA协同生成候选架构x_a。\n- 代理模型g(x_a)进行奖励预测ˆR_t。\n`C1: 3.0, C2: 4.0, C3: 6.5`', 'color': '#45B7D1', 'narration': '在每个强化学习时间步，两个智能体协同生成新的候选架构，并由预训练的代理模型快速进行奖励预测。'}],
            title="MARCO框架示例",
            subtitle="共形预测过滤机制的详细操作",
            intro_narration="为了更直观地理解CP过滤的工作原理，我们通过具体示例拆解其操作流程。",
            outro_narration="这种分层过滤机制，确保了计算资源的优化配置，是MARCO搜索时间大幅减少的关键。"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
