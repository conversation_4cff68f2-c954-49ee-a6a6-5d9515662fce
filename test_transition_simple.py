"""
简化版转场系统测试脚本 - 不使用语音功能
"""

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.core.transition_effects import TransitionManager
import shutil
from pathlib import Path


class SimpleTransitionTestScene(FeynmanScene):
    """简化版转场测试场景"""
    
    def construct(self):
        # 禁用语音功能
        self.skip_animations = False
        
        # 清理之前的状态文件
        states_dir = Path("temp/scene_states")
        if states_dir.exists():
            shutil.rmtree(states_dir)
        
        # 测试转场效果
        self.test_transition_effects()
    
    def test_transition_effects(self):
        """测试各种转场效果"""
        self.logger.info("开始测试转场效果")
        
        # 创建第一个内容
        content1 = VGroup(
            Text("第一个内容", font_size=48, color=BLUE),
            Text("这是第一个测试内容", font_size=24, color=WHITE).shift(DOWN)
        )
        
        self.play(FadeIn(content1))
        self.current_mobj = content1
        self.save_scene_state("content_1", "end")
        self.wait(1)
        
        # 创建第二个内容
        content2 = VGroup(
            Text("第二个内容", font_size=48, color=GREEN),
            Text("这是第二个测试内容", font_size=24, color=WHITE).shift(DOWN)
        )
        
        # 测试滑动转场
        self.logger.info("应用滑动转场效果")
        TransitionManager.apply_transition(
            scene=self,
            old_mobj=self.current_mobj,
            new_mobj=content2,
            transition_type="slide_left",
            run_time=1.5
        )
        
        self.current_mobj = content2
        self.save_scene_state("content_2", "end")
        self.wait(1)
        
        # 创建第三个内容
        content3 = VGroup(
            Text("第三个内容", font_size=48, color=RED),
            Text("这是第三个测试内容", font_size=24, color=WHITE).shift(DOWN)
        )
        
        # 测试缩放转场
        self.logger.info("应用缩放转场效果")
        TransitionManager.apply_transition(
            scene=self,
            old_mobj=self.current_mobj,
            new_mobj=content3,
            transition_type="zoom",
            run_time=1.5
        )
        
        self.current_mobj = content3
        self.save_scene_state("content_3", "end")
        self.wait(1)
        
        # 创建第四个内容
        content4 = VGroup(
            Text("第四个内容", font_size=48, color=YELLOW),
            Text("这是第四个测试内容", font_size=24, color=WHITE).shift(DOWN)
        )
        
        # 测试旋转转场
        self.logger.info("应用旋转转场效果")
        TransitionManager.apply_transition(
            scene=self,
            old_mobj=self.current_mobj,
            new_mobj=content4,
            transition_type="rotate",
            run_time=2.0
        )
        
        self.current_mobj = content4
        self.save_scene_state("content_4", "end")
        self.wait(2)


class ManualTransitionTestScene(Scene):
    """手动测试各种转场效果"""
    
    def construct(self):
        # 测试所有转场效果
        effects = [
            ("fade", "淡入淡出"),
            ("slide_left", "向左滑动"),
            ("slide_right", "向右滑动"),
            ("slide_up", "向上滑动"),
            ("slide_down", "向下滑动"),
            ("zoom", "缩放"),
            ("rotate", "旋转"),
            ("spiral", "螺旋"),
        ]
        
        for i, (effect_name, effect_desc) in enumerate(effects):
            # 创建当前内容
            current_content = VGroup(
                Text(f"转场效果 {i+1}", font_size=36, color=BLUE),
                Text(effect_desc, font_size=24, color=WHITE).shift(DOWN * 0.8),
                Text(f"效果名称: {effect_name}", font_size=18, color=GRAY).shift(DOWN * 1.5)
            )
            
            if i == 0:
                # 第一个内容直接显示
                self.play(FadeIn(current_content))
                self.wait(1)
            else:
                # 后续内容使用转场效果
                transition_func = TransitionManager.get_transition_function(effect_name)
                animations = transition_func(previous_content, current_content, run_time=1.5)
                
                if animations:
                    self.play(*animations, run_time=1.5)
                else:
                    self.play(FadeOut(previous_content), FadeIn(current_content))
                
                self.wait(1)
            
            previous_content = current_content
        
        # 最后清除
        self.play(FadeOut(previous_content))


def test_simple_transitions():
    """测试简化版转场"""
    print("测试简化版转场效果...")
    
    import os
    os.system("manim test_transition_simple.py SimpleTransitionTestScene -ql")
    
    print("简化版转场测试完成！")


def test_manual_transitions():
    """测试手动转场效果"""
    print("测试手动转场效果...")
    
    import os
    os.system("manim test_transition_simple.py ManualTransitionTestScene -ql")
    
    print("手动转场测试完成！")


def check_state_files():
    """检查状态文件"""
    states_dir = Path("temp/scene_states")
    if states_dir.exists():
        print(f"\n状态文件目录: {states_dir}")
        for file in states_dir.glob("*.json"):
            print(f"  - {file.name}")
            try:
                import json
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"    场景ID: {data.get('scene_id')}")
                    print(f"    状态类型: {data.get('state_type')}")
                    print(f"    对象类型: {data.get('mobj_type')}")
            except Exception as e:
                print(f"    读取失败: {e}")
    else:
        print("状态文件目录不存在")


def main():
    """主测试函数"""
    print("=" * 50)
    print("简化版转场系统测试")
    print("=" * 50)
    
    # 测试简化版转场
    test_simple_transitions()
    
    # 检查状态文件
    check_state_files()
    
    # 测试手动转场
    test_manual_transitions()
    
    print("\n所有测试完成！")
    print("检查以下文件:")
    print("- media/videos/test_transition_simple/480p15/SimpleTransitionTestScene.mp4")
    print("- media/videos/test_transition_simple/480p15/ManualTransitionTestScene.mp4")


if __name__ == "__main__":
    main()
