"""
转场效果使用演示 - 展示如何在实际动画函数中使用转场效果
"""

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions.animate_markdown import animate_markdown
from dsl.v2.core.inter_scene_transition import generate_inter_scene_transition
import shutil
from pathlib import Path


class TransitionUsageDemo(FeynmanScene):
    """转场效果使用演示场景"""

    def construct(self):
        # 清理之前的状态文件
        states_dir = Path("temp/scene_states")
        if states_dir.exists():
            shutil.rmtree(states_dir)

        # 设置转场参数
        self.transition_enabled = True
        self.default_transition_type = None  # 随机选择
        self.transition_run_time = 1.5

        self.demo_markdown_transitions()

    def demo_markdown_transitions(self):
        """演示Markdown动画函数之间的转场效果"""
        self.logger.info("开始演示Markdown转场效果")

        # 第一个内容 - 介绍
        animate_markdown(
            scene=self,
            content="""# 转场效果演示

## 欢迎来到转场效果演示

这是第一个内容，展示了新的转场系统的能力。

- ✨ 精美的转场效果
- 🎬 多种转场类型
- 🔄 自动状态保存""",
            narration="这是第一个内容，展示了新的转场系统的能力。",
            id="intro_content"
        )

        self.wait(2)

        # 第二个内容 - 转场类型介绍
        animate_markdown(
            scene=self,
            content="""# 转场类型介绍

## 支持的转场效果

### 基础转场
- **淡入淡出** - 经典的渐变效果
- **滑动** - 左右上下滑动
- **缩放** - 放大缩小效果

### 高级转场
- **旋转** - 旋转切换
- **螺旋** - 螺旋消失
- **翻转** - 水平翻转""",
            narration="支持多种转场类型，包括基础和高级效果。",
            id="transition_types"
        )

        self.wait(2)

        # 第三个内容 - 技术特性
        animate_markdown(
            scene=self,
            content="""# 技术特性

## 转场系统特性

### 同一分镜内转场
- 自动应用转场效果
- 可配置转场类型
- 支持随机选择

### 分镜间转场
- 状态序列化保存
- 独立转场视频生成
- 支持视频拼接

### 配置选项
- 可启用/禁用转场
- 自定义转场时长
- 指定转场类型""",
            narration="转场系统具有多种技术特性，包括自动转场、状态保存和视频拼接。",
            id="technical_features"
        )

        self.wait(2)

        # 第四个内容 - 使用方法
        animate_markdown(
            scene=self,
            content="""# 使用方法

## 如何使用转场效果

### 1. 启用转场
```python
scene.transition_enabled = True
scene.default_transition_type = "slide_left"
scene.transition_run_time = 1.5
```

### 2. 自动转场
动画函数会自动应用转场效果：
- 调用 `clear_current_mobj()` 时触发
- 自动保存场景状态
- 支持多种转场类型

### 3. 分镜间转场
```python
generate_inter_scene_transition(
    from_scene_id="scene_a",
    to_scene_id="scene_b",
    transition_type="zoom"
)
```""",
            narration="使用转场效果非常简单，只需要在场景中启用转场即可。",
            id="usage_guide"
        )

        self.wait(2)

        # 第五个内容 - 结束
        animate_markdown(
            scene=self,
            content="""# 演示完成

## 🎉 转场效果演示结束

感谢观看转场效果演示！

### 主要收获
- ✅ 了解了转场系统的功能
- ✅ 看到了各种转场效果
- ✅ 学会了如何使用转场

### 下一步
- 在实际项目中使用转场
- 尝试不同的转场类型
- 优化转场参数设置

**让你的视频更加精彩！** ✨""",
            narration="以上就是转场效果演示的全部内容，感谢您的观看！",
            id="conclusion"
        )

        self.wait(3)


def demo_inter_scene_transition():
    """演示分镜间转场生成"""
    print("演示分镜间转场生成...")

    # 首先渲染主演示场景
    import os
    os.system("manim demo_transition_usage.py TransitionUsageDemo -ql")

    # 生成一些分镜间转场
    transitions = [
        ("intro_content", "transition_types", "slide_left"),
        ("transition_types", "technical_features", "zoom"),
        ("technical_features", "usage_guide", "rotate"),
        ("usage_guide", "conclusion", "spiral"),
    ]

    for i, (from_id, to_id, transition_type) in enumerate(transitions):
        output_file = f"transition_{i+1}_{transition_type}.py"
        success = generate_inter_scene_transition(
            from_scene_id=from_id,
            to_scene_id=to_id,
            transition_type=transition_type,
            output_file=output_file
        )

        if success:
            print(f"✅ 生成转场 {i+1}: {from_id} -> {to_id} ({transition_type})")
            # 尝试渲染转场视频
            try:
                os.system(f"manim {output_file} InterSceneTransitionScene -ql")
                print(f"   渲染成功: {output_file}")
            except Exception as e:
                print(f"   渲染失败: {e}")
        else:
            print(f"❌ 生成转场 {i+1} 失败")


def show_transition_config():
    """显示转场配置示例"""
    print("\n" + "="*50)
    print("转场配置示例")
    print("="*50)

    config_example = """
# 在 FeynmanScene 中配置转场
class MyScene(FeynmanScene):
    def construct(self):
        # 启用转场效果
        self.transition_enabled = True

        # 设置默认转场类型（None表示随机选择）
        self.default_transition_type = "slide_left"

        # 设置转场动画时长
        self.transition_run_time = 1.5

        # 使用动画函数，转场会自动应用
        animate_markdown(scene=self, content="...", id="content1")
        animate_markdown(scene=self, content="...", id="content2")

# 可用的转场类型
transition_types = [
    "fade",         # 淡入淡出
    "slide_left",   # 向左滑动
    "slide_right",  # 向右滑动
    "slide_up",     # 向上滑动
    "slide_down",   # 向下滑动
    "zoom",         # 缩放
    "rotate",       # 旋转
    "wipe_left",    # 擦除
    "circle_wipe",  # 圆形擦除
    "flip",         # 翻转
    "spiral",       # 螺旋
]

# 手动应用转场
from dsl.v2.core.transition_effects import TransitionManager

TransitionManager.apply_transition(
    scene=self,
    old_mobj=old_content,
    new_mobj=new_content,
    transition_type="zoom",
    run_time=2.0
)
"""

    print(config_example)


def main():
    """主演示函数"""
    print("=" * 60)
    print("转场效果使用演示")
    print("=" * 60)

    # 显示配置示例
    show_transition_config()

    # 演示分镜间转场
    demo_inter_scene_transition()

    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    print("\n检查以下文件:")
    print("- media/videos/demo_transition_usage/480p15/TransitionUsageDemo.mp4")
    print("- transition_*.py (生成的转场代码)")
    print("- media/videos/transition_*/480p15/InterSceneTransitionScene.mp4")
    print("- temp/scene_states/ (状态文件)")


if __name__ == "__main__":
    main()
