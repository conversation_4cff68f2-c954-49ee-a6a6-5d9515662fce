#!/usr/bin/env python3
"""
材料代理 - 重构简化版本
移除过度设计的逻辑，专注于核心功能
参考github_source_agent_refactored.py的重构思路
"""

# import logging
import os
import re
import sys
from typing import Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
# logger = CommonUtils.setup_logging()
# # 确保当前模块的日志级别为INFO
# logger.setLevel(logging.INFO)
from camel.agents import ChatAgent
from camel.toolkits import MCPToolkit
from loguru import logger

from agents.material_enhancement import MaterialEnhancer
from utils.common import AgentFactory, CommonUtils, Config, MediaClassifier


class MultimediaExtractor:
    """简化的多媒体提取器"""

    @staticmethod
    def extract_elements(content: str) -> dict[str, list[dict]]:
        """提取并分类多媒体元素"""
        elements = {
            "images": [],
            "videos": [],
            "code_blocks": [],
            "tables": [],
            "important_media": [],  # 重要素材单独列出
        }

        # 提取图片
        image_pattern = r"!\[([^\]]*)\]\(([^)]+)\)"
        for match in re.finditer(image_pattern, content):
            alt_text, url = match.groups()
            element_text = f"{alt_text} {url}"
            is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "image")

            image_info = {
                "alt": alt_text,
                "url": url,
                "is_important": is_important,
                "category": category,
                "description": enhanced_desc,
            }
            elements["images"].append(image_info)
            if is_important:
                elements["important_media"].append(image_info)

        # 提取视频链接
        video_pattern = r"\[([^\]]*)\]\(([^)]*\.(?:mp4|avi|mov|wmv|flv|webm)[^)]*)\)"
        for match in re.finditer(video_pattern, content):
            text, url = match.groups()
            element_text = f"{text} {url}"
            is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "video")

            video_info = {
                "text": text,
                "url": url,
                "is_important": is_important,
                "category": category,
                "description": enhanced_desc,
            }
            elements["videos"].append(video_info)
            if is_important:
                elements["important_media"].append(video_info)

        # 提取代码块
        code_pattern = r"```(\w*)\n(.*?)\n```"
        for match in re.finditer(code_pattern, content, re.DOTALL):
            language, code = match.groups()
            element_text = f"{language} {code[:100]}"  # 只取前100字符分析
            is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "code")

            code_info = {
                "language": language,
                "code": code,
                "is_important": is_important,
                "category": category,
                "description": enhanced_desc,
            }
            elements["code_blocks"].append(code_info)
            if is_important:
                elements["important_media"].append(code_info)

        # 提取表格（简化检测）
        table_pattern = r"\|.*\|.*\n\|[-\s|]+\|.*\n(\|.*\|.*\n)+"
        for match in re.finditer(table_pattern, content):
            table_content = match.group(0)
            element_text = table_content[:200]  # 只取前200字符分析
            is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "table")

            table_info = {
                "content": table_content,
                "is_important": is_important,
                "category": category,
                "description": enhanced_desc,
            }
            elements["tables"].append(table_info)
            if is_important:
                elements["important_media"].append(table_info)

        logger.info(
            f"提取多媒体元素: 图片{len(elements['images'])}个, 视频{len(elements['videos'])}个, "
            f"代码块{len(elements['code_blocks'])}个, 表格{len(elements['tables'])}个, "
            f"重要素材{len(elements['important_media'])}个"
        )

        return elements


class MaterialAgent:
    """简化的材料代理主类"""

    def __init__(
        self,
        config_path: str = "config/config.yaml",
        enhancement_config: Optional[dict[str, bool]] = None,
        mcp_toolkit: MCPToolkit = None,
    ):
        """
        初始化材料代理

        Args:
            config_path: 配置文件路径
            enhancement_config: 扩充策略配置
        """
        self.config = Config(config_path)
        self.model = AgentFactory.create_model(self.config)
        self.agents = self._create_agents(mcp_toolkit)
        for name, agent in self.agents.items():
            logger.info(f"创建代理: {name}, {[tool['function']['name'] for tool in agent._get_full_tool_schemas()]}")
        self.extractor = MultimediaExtractor()
        # 传递完整的config字典和model给MaterialEnhancer
        self.enhancer = MaterialEnhancer(enhancement_config, self.config.config, self.model)

    def _create_agents(self, mcp_toolkit: MCPToolkit = None) -> dict[str, ChatAgent]:
        """创建AI代理"""
        return {
            "generator": AgentFactory.create_analyzer_agent(
                self.model,
                "内容生成专家",
                "你负责根据用户目的和原始材料生成优化的视频讲解内容。专注于突出核心认知、关键结论和重要发现。",
                mcp_toolkit,
            ),
            "reviewer": AgentFactory.create_reviewer_agent(
                self.model, "内容审查员", "你负责审查和完善内容，确保逻辑清晰、重点突出且易于理解。", mcp_toolkit
            ),
        }

    def _calculate_target_length(self, purpose: str) -> int:
        """根据purpose中的视频时长计算目标markdown长度"""
        # 提取时长数字（支持"5分钟"、"10分钟"、"25分钟"等格式）
        import re

        time_match = re.search(r"(\d+)\s*分钟", purpose)
        if time_match:
            minutes = int(time_match.group(1))
        else:
            # 如果没有明确时长，默认10分钟
            minutes = 10

        # 简单映射：每分钟大约800-1000字符的markdown内容
        base_chars_per_minute = 900
        target_length = minutes * base_chars_per_minute

        # 设置合理的上下限
        min_length = 2000  # 最少2000字符
        max_length = 30000  # 最多30000字符

        return max(min_length, min(target_length, max_length))

    def read_material(self, material_path: str) -> str:
        """读取材料内容"""
        if not material_path:
            return ""
        return CommonUtils.read_file(material_path)

    def generate_material(self, material_content: str, purpose: str, output_dir: str = "output") -> str:
        """生成优化的材料内容 - 使用新的扩充架构"""
        # logger.info("开始生成优化材料")  # 关闭详细生成日志

        # 0. 计算目标内容长度
        target_length = self._calculate_target_length(purpose)
        logger.info(f"0. 根据视频时长计算目标内容长度: {target_length} 字符")

        # 1. 提取多媒体元素并修正路径（基于原始材料）
        logger.info("1. 开始提取多媒体元素并修正路径")
        multimedia_elements = {}
        important_media_info = ""
        if material_content:
            multimedia_elements = self.extractor.extract_elements(material_content)

            # 修正多媒体元素路径，确保都以output/项目名/开头
            project_name = os.path.basename(output_dir)
            corrected_media = []

            for media in multimedia_elements["important_media"][:10]:
                # 获取原始路径
                original_url = media.get("url", "")

                # 如果路径不是以output/开头，需要修正
                if original_url and not original_url.startswith("output/"):
                    # 检查是否是相对路径（如 media/xxx.png）
                    if not original_url.startswith(("http://", "https://", "/")):
                        corrected_url = f"output/{project_name}/{original_url}"
                        media["url"] = corrected_url

                corrected_media.append(media)

            # 构建重要素材信息
            if corrected_media:
                important_media_info = "\n**重要多媒体素材**：\n"
                for i, media in enumerate(corrected_media):
                    media_type = media.get("category", "other")
                    description = media.get("description", "无描述")
                    url = media.get("url", "")
                    important_media_info += f"{i+1}. {description} (类型: {media_type})\n"
                    if url:
                        important_media_info += f"   路径: {url}\n"

            # 截断材料内容，避免超长
            material_content = material_content[:300000]

        # 2. 构建生成提示（集成工具插桩能力）
        # 构造所有可用工具的描述信息
        import json

        tool_infos = []
        for idx, tool in enumerate(self.enhancer.tools, start=1):
            try:
                tool_desc = f"### 工具{idx}: {tool.tool_name}\n"
                info = tool.get_tool_info()
                if isinstance(info, dict):
                    info = json.dumps(info, ensure_ascii=False, indent=2)
                tool_desc += info
                tool_infos.append(tool_desc)
            except Exception as e:
                logger.warning(f"工具 {getattr(tool, 'tool_name', str(tool))} 获取信息失败: {e}")
        tool_infos_str = "\n\n".join(tool_infos)

        generation_prompt = f"""
你是一个内容讲解专家。在生成结构化视频讲解稿的同时，智能地判断是否需要调用某个工具进行内容补充，并在需要扩展的地方插入如下格式的特殊标记：
<<TOOL:{{\"need_tool\": \"工具名\", \"tool_focus\": \"工具在生成扩展内容时需要关注的焦点，可以结合要讲解的内容以及讲解目的来指导工具生成最能帮助讲解的辅助材料\", \"suggested_usage\": \"工具的输出如何辅助讲解\", \"thinking\": \"在选择工具时的思考过程\"}}>>。

**用户目的**:
<purpose>
{purpose}
</purpose>

**目标内容长度**: 约{target_length}字符（根据视频时长自动计算）

**原始材料**:
<original_material>
{material_content}
</original_material>

**重要素材**：
<important_media_info>
{important_media_info}
</important_media_info>

**可用工具列表**：
<tools_list>
{tool_infos_str}
</tools_list>

**正文内容生成要求**：
1. **长度控制**：
  - 生成的markdown内容应控制在{target_length}字符左右（误差±20%可接受）
  - 根据目标长度调整内容详细程度：短视频重点突出，长视频可以详细展开
  - 合理分配各部分篇幅，确保重点内容有足够空间

2. **自适应内容结构**：根据用户目的自动决定：
  - 讲解提纲和重点章节
  - 内容详细程度和讲解深度
  - 适合的表达方式和语言风格
  - 素材的选用和展示优先级
  - 可以参考原素材的讲解逻辑和重点

3. **重要素材优先**：
  - 优先使用演示Demo、架构图、流程图等核心素材
  - 在合适的章节位置插入重要多媒体内容
  - 为重要素材提供详细的讲解说明
  - 引用的素材链接必须使用完整路径，格式为：output/项目名/xxx，例如 ![架构图](output/{os.path.basename(output_dir)}/media/architecture.png)
  - 所有多媒体素材路径都必须以 output/{os.path.basename(output_dir)}/ 开头
  - 如果输入没有重要素材，输出markdown文件不要生造出来引用

4. **核心内容保留**：
  - 必须保留原材料中的核心认知、关键结论、重要发现
  - 不能遗漏关键技术点和创新之处
  - 保持原有逻辑结构的合理性

5. **精炼表达**：
  - 内容精炼不冗余，但要包含必要细节
  - 输出markdown文档的逻辑一定符合用户目的，逻辑清晰，不能跳跃性，不能缺失，比如一般论文适合"工作核心价值-核心架构创新-实验效果-批判性总结这类结构
  - 文案表述减少虚无、套话，都用实在的表述，不要浪费一个字，要言之有物

6. **特殊处理规则**：
  - GitHub项目：开篇突出项目热度和Stars数，重点展示Demo效果,Readme里核心能力介绍要完整，不要遗漏
  - 学术论文：开篇要强调研究机构（从作者信息中提取）、实验效果（从实验数据中提取）等关键信息，但不要过度夸大，要客观真实
  - 除以上两种类型，根据内容特点,按Github项目和学术论文例子，突出素材与其他类型不同的要素，自适应处理

**工具调用规则**：
1. 工具使用分析：生成正文内容时，结合可用工具列表和用户purpose，分析哪些地方适合插入什么工具调用标记，如需要录屏适合开篇、时间线适合演进讲解，例子解释适合复杂算法概念解释、深度洞察等补充
2. 工具调用格式：<<TOOL:{{"need_tool": "工具名", "tool_focus": "工具在生成扩展内容时需要关注的焦点，可以结合要讲解的内容以及讲解目的，来指导工具生成最能帮助讲解的辅助材料", "suggested_usage": "工具的输出如何辅助讲解", "thinking": "在选择工具时的思考过程，包括但不限于选择工具的依据，或者遇到的一些可能会影响判断，需要进一步明确的问题等"}}>>
3. 工具调用位置：在正文内容中真正需要扩展的地方，插入工具调用标记，不要遗漏应补充的地方

**输出格式**：
```markdown
# [引人注目的标题]

## 内容概览
- **核心主题**：[主题描述]
- **目标受众**：[自动判断的目标受众]
- **主要收获**：[观众能获得的核心价值]
- **时间长度**：[视频时间长度]

## [第一部分标题]
[使用重要素材的开篇内容，适当插入工具调用标记]

## [第二部分标题]
[核心内容讲解，适当插入重要素材、工具调用标记]

## [第三部分标题]
[深入分析或实践应用，适当插入重要素材、工具调用标记]

## 关键要点总结
[核心认知和关键结论的提炼，适当插入重要素材或工具调用标记]

## 工具调用情况总结的严格要求
1. 如果某个工具在整个讲解文案生成过程中从来没有被调用，必须在文档最后列出**所有未被调用**的工具列表（可用工具参考前面），并简单说明为什么没有被调用。
2. 针对用户目的，内容讲解需要调用的工具但缺失，**必须列出**所有这样的工具和对应的简要内容说明，以便持续改进和完善工具。
3. 针对用户目的和内容讲解，分析需要补充什么内容，更容易达到用户目的，更易懂易记住，**必须列出**简述内容说明，以便持续改进和完善内容。
```

请直接输出优化后的markdown内容（含工具调用标记），不要包含任何解释说明。
"""

        # 3. 使用生成代理创建内容
        from camel.messages import BaseMessage

        logger.info(
            f"3. 使用生成代理创建内容: characters {len(generation_prompt)}, words {len(generation_prompt.split())}"
        )

        debug_file = os.path.join(output_dir, "generation_prompt.txt")
        with open(debug_file, "w") as fout:
            fout.write(generation_prompt)
        logger.info(f"生成提示词已保存到: {debug_file}")

        # generated_content = (
        #     self.agents["generator"]
        #     .step(BaseMessage.make_user_message(role_name="User", content=generation_prompt))
        #     .msg.content
        # )
        result = self.agents["generator"].step(BaseMessage.make_user_message(role_name="User", content=generation_prompt))
        if result.msg is not None:
            generated_content = result.msg.content
        else:
            logger.error(result.model_dump_json())
            raise Exception("生成代理未返回响应")

        debug_file = os.path.join(output_dir, "generated_content_with_tool.txt")
        with open(debug_file, "w") as fout:
            fout.write(generated_content)
        logger.info(f"带有工具调用说明的生成内容已保存到: {debug_file}")

        # 4. 使用审查代理优化内容
        review_prompt = f"""
请审查并优化以下内容，确保：

1. **长度适配**：内容长度应控制在{target_length}字符左右，根据视频时长需求调整详细程度
2. **结构完整性**：标题层次清晰，内容逻辑合理
3. **核心价值突出**：重要认知和关键结论得到充分体现
4. **素材使用恰当**：重要多媒体素材在合适位置发挥作用
5. **表达精炼准确**：语言流畅，重点突出，易于理解,不要浪费一个字，要言之有物，不要使用虚无的表述
6. **用户目的达成**：内容符合用户的具体需求和目标
7. **素材引用正确**：
   - 检查每一个素材链接，确保路径格式为 output/{os.path.basename(output_dir)}/xxx
   - 去掉不在以下重要素材中的链接
   - 确保所有图片、视频等多媒体链接都以 output/{os.path.basename(output_dir)}/ 开头
   - 如果是论文，研究机构一定要包含，实验效果是否包含
8. **工具调用正确**：
   - 检查每一个工具调用是否合适，确保符合讲解目的，能起到补充最合适的素材的作用
   - 检查是否遗漏重要地方，需要使用工具补充素材的，比如开篇录屏视频起到吸引人的作用
   - 检查工具调用格式是否正确，满足 <<TOOL:{{"need_tool": "工具名", "tool_focus": "工具在生成扩展内容时需要关注的焦点，可以结合要讲解的内容以及讲解目的，来指导工具生成最能帮助讲解的辅助材料", "suggested_usage": "工具的输出如何辅助讲解", "thinking": "在选择工具时的思考过程"}}> 的格式

**可用的重要素材列表**：
<important_media_info>
{important_media_info}
</important_media_info>

原始内容：
<original_content>
{generated_content}
</original_content>

**可用工具列表**：
<tools_list>
{tool_infos_str}
</tools_list>

请直接输出优化后的最终内容，不要包含审查说明。
"""

        logger.info(f"4. 使用审查代理优化内容: characters {len(review_prompt)}, words {len(review_prompt.split())}")

        final_content = (
            self.agents["reviewer"]
            .step(BaseMessage.make_user_message(role_name="User", content=review_prompt))
            .msg.content
        )

        enhanced_content = final_content

        # 5. 智能素材扩充步骤（根据模式选择输入内容）
        # logger.info("5. 开始基于生成内容执行智能素材扩充步骤")
        # chat模式使用final_content，其他模式使用material_content
        # 这里需要同时传入original_content和material_content
        # 假设final_content为original_content, material_content为简化内容
        # enhancement_result = self.enhancer.enhance_material(final_content, material_content, purpose, output_dir)
        # logger.info(f"智能扩充完成：应用了 {enhancement_result.get('enhancement_count', 0)} 个工具")

        # 6. 使用融合工具结果的完整内容
        # enhanced_content = enhancement_result.get("enhanced_content", final_content)

        # 7. 可选：在文档末尾附加工具调用明细（如有需要）
        # 可自定义appendix格式
        # enhancements = enhancement_result.get("enhancements", [])
        # if enhancements:
        #     appendix = "\n\n---\n**自动工具增强明细**\n" + "\n".join([
        #         f"- 工具: {e['tool_name']} 用途: {e['suggested_usage']}" for e in enhancements
        #     ])
        #     enhanced_content += appendix

        return enhanced_content

    def _append_tool_sections(self, content: str, enhancement_result: dict[str, Any]) -> str:
        """通用方法：添加工具返回的章节内容"""
        try:
            enhancements = enhancement_result.get("enhancements", [])

            for enhancement in enhancements:
                if enhancement.get("status") == "success":
                    appendix_section = enhancement.get("appendix_section", "")
                    if appendix_section:
                        tool_name = enhancement.get("tool_name", "unknown")

                        # 对于六维评估工具，需要特殊处理插入位置
                        if tool_name == "six_dimensions_evaluation":
                            content = self._insert_section_after_pattern(
                                content,
                                appendix_section,
                                r"(## 📊 六维度评估.*?(?=\n## [^📊]|\n# |\Z))",
                                "六维度评估章节后",
                            )
                        else:
                            # 其他工具直接追加到末尾
                            content = content + "\n\n" + appendix_section
                            logger.info(f"已添加 {tool_name} 工具的章节内容")

            return content
        except Exception as e:
            logger.warning(f"添加工具章节内容失败: {e}")
            return content

    def _insert_section_after_pattern(self, content: str, section: str, pattern: str, description: str) -> str:
        """在匹配的模式后插入章节内容"""
        try:
            match = re.search(pattern, content, re.DOTALL)
            if match:
                end_pos = match.end()
                new_content = content[:end_pos] + section + content[end_pos:]
                logger.info(f"已在{description}插入章节内容")
                return new_content
            else:
                logger.info("未找到匹配模式，将章节内容追加到末尾")
                return content + "\n\n" + section
        except Exception as e:
            logger.warning(f"插入章节内容失败: {e}")
            return content

    def save_material(self, content: str, output_file: str = None) -> str:
        """保存生成的材料"""
        if not output_file:
            output_file = f"output/optimized_material_{int(__import__('time').time())}.md"

        CommonUtils.save_file(content, output_file)
        return output_file

    def apply_tools_and_merge(self, original_content: str, marked_content: str, output_dir: str = "output"):
        """
        解析插桩内容，遇到<<TOOL:{...}>>标记时自动调用工具，将结果插入对应位置。
        其余内容原样拼接，返回最终增强内容和所有工具调用结果。
        """
        import json
        import re

        pattern = r"<<TOOL:(\{.*?\})>>"
        pos = 0
        enhanced_parts = []
        enhancements = []
        for match in re.finditer(pattern, marked_content, flags=re.DOTALL):
            start, end = match.span()
            # 拼接前一段普通内容
            enhanced_parts.append(marked_content[pos:start])
            marker_json = match.group(1)
            try:
                marker = json.loads(marker_json)
                tool_name = marker.get("need_tool")
                tool_focus = marker.get("tool_focus")
                suggested_usage = marker.get("suggested_usage")
                # 查找工具
                tool = next((t for t in self.enhancer.tools if getattr(t, "tool_name", None) == tool_name), None)
                if not tool:
                    tool_result = f"[⚠️ 未找到工具: {tool_name}]"
                    logger.warning(f"未找到工具: {tool_name}")
                else:
                    logger.info(f"调用工具: {tool_name}")
                    try:
                        result = tool.apply_tool(original_content, output_dir, context={}, focus=tool_focus)
                        logger.info(f"{tool_name} 调用结果: {result}")
                        enhancements.append(
                            {
                                "tool_name": tool_name,
                                "tool_focus": tool_focus,
                                "result": result,
                                "suggested_usage": suggested_usage,
                            }
                        )
                        tool_result = f"\n<补充素材>\n<工具名>\n{tool_name}\n</工具名>\n<用途>\n{suggested_usage}\n</用途>\n<工具输出>\n{result}\n</工具输出>\n</补充素材>"
                    except Exception as e:
                        tool_result = f"[⚠️ 工具调用异常: {tool_name} - {e}]"
                        logger.error(f"工具调用异常: {tool_name} - {e}")
                enhanced_parts.append(tool_result)
            except Exception as e:
                enhanced_parts.append(f"[⚠️ 工具标记解析失败: {e}]")
                logger.error(f"工具标记解析失败: {e}")
            pos = end
        # 拼接最后一段内容
        enhanced_parts.append(marked_content[pos:])
        enhanced_content = "".join(enhanced_parts)
        return enhanced_content, enhancements

    def run(
        self, material_path: str, purpose: str, output_file: str = None, disable_tools: bool = False
    ) -> dict[str, Any]:
        """主运行方法 - 简化的完整流程"""
        try:
            logger.info(f"开始处理材料: {material_path}")
            logger.info(f"用户目的: {purpose}")

            # 1. 读取材料
            material_content = self.read_material(material_path)
            if not material_content:
                logger.info("材料内容为空，启动chat模式")

            # 2. 确定输出目录
            if output_file:
                output_dir = os.path.dirname(output_file)
            else:
                # 如果有输入文件，使用其名称作为项目名
                if material_path:
                    project_name = os.path.splitext(os.path.basename(material_path))[0]
                else:
                    project_name = f"chat_material_{int(__import__('time').time())}"
                output_dir = f"output/{project_name}"

            # 3. 生成优化内容（包含素材扩充）
            optimized_content = self.generate_material(material_content, purpose, output_dir)

            # 3.5 工具标记处理与融合
            if not disable_tools:
                enhanced_content, enhancements = self.apply_tools_and_merge(
                    material_content, optimized_content, output_dir
                )
                for enhancement in enhancements:
                    logger.info(f"工具 {enhancement['tool_name']} 调用结果: {enhancement['result']}")
            else:
                enhanced_content = optimized_content
                enhancements = []

            # 4. 保存结果
            saved_file = self.save_material(enhanced_content, output_file)

            # 5. 返回结果
            return {
                "status": "success",
                "input_file": material_path,
                "output_file": saved_file,
                "output_dir": output_dir,
                "purpose": purpose,
                "content_length": len(optimized_content),
                "message": "材料优化完成（使用模块化工具架构）",
            }

        except Exception as e:
            logger.error(f"处理失败: {e}")
            import traceback

            traceback.print_exc()
            return {"status": "error", "error": str(e), "message": "材料处理失败"}


def main():
    """主函数"""

    import argparse

    parser = argparse.ArgumentParser(description="智能材料优化工具（采用模块化工具架构，支持智能工具选择和雷达图生成）")
    parser.add_argument("--material", "-m", type=str, help="输入材料文件路径")
    parser.add_argument("--purpose", "-p", type=str, help="用户目的描述")
    parser.add_argument("--output", "-o", type=str, help="输出文件路径")
    parser.add_argument("--config", "-c", type=str, default="config/config.yaml", help="配置文件路径")

    # 智能工具开关
    parser.add_argument("--enable-video", action="store_true", help="强制启用录屏工具")
    parser.add_argument("--disable-video", action="store_true", help="禁用录屏工具")
    parser.add_argument("--enable-image", action="store_true", help="启用图片生成工具")
    parser.add_argument("--enable-audio", action="store_true", help="启用音频合成工具")
    parser.add_argument("--enable-timeline", action="store_true", help="启用时间轴生成工具")
    parser.add_argument("--disable-timeline", action="store_true", help="禁用时间轴生成工具")
    parser.add_argument("--disable-tools", action="store_true", help="禁用所有工具")
    parser.add_argument("--use-mcp", action="store_true", help="使用MCP工具")

    args = parser.parse_args()

    try:
        if args.use_mcp:
            mcp_config_path = "config/mcp_servers_config.json"
            mcp_toolkit = MCPToolkit(config_path=mcp_config_path)
            mcp_toolkit.connect_sync()
        else:
            mcp_toolkit = None
        # 创建agent，传递config路径
        agent = MaterialAgent(args.config, mcp_toolkit=mcp_toolkit)

        # 只有在明确指定命令行参数时才覆盖config配置
        enhancement_override = None
        if (
            args.enable_video
            or args.disable_video
            or args.enable_image
            or args.enable_audio
            or args.enable_timeline
            or args.disable_timeline
        ):
            enhancement_override = {
                "video_recording": (args.enable_video or not args.disable_video) and not args.disable_video,
                "image_generation": args.enable_image,
                "audio_synthesis": args.enable_audio,
                "timeline_generation": (args.enable_timeline or not args.disable_timeline)
                and not args.disable_timeline,
            }
            # 重新创建agent使用命令行覆盖的配置
            agent = MaterialAgent(args.config, enhancement_override)

        # 使用命令行参数或提供默认值
        material_path = args.material
        purpose = args.purpose
        output_file = args.output

        # 如果没有提供必要参数，给出提示和默认值
        if not purpose:
            print("❌ 错误: 请指定用户目的")
            print("使用方法: python material_agent_refactored.py -m <材料文件> -p '<目的描述>'")
            print(
                "示例: python material_agent_refactored.py -m output/project_analysis.md -p '为技术爱好者制作5分钟视频介绍'"
            )
            print("\n智能工具选项:")
            print("  --enable-video     强制启用录屏工具")
            print("  --disable-video    禁用录屏工具")
            print("  --enable-image     启用图片生成工具")
            print("  --enable-audio     启用音频合成工具")
            print("  --enable-timeline  启用时间轴生成工具")
            print("  --disable-timeline 禁用时间轴生成工具")
            print("\n配置说明: 默认从config.yaml读取工具配置，命令行参数可覆盖，系统会根据内容和目标智能选择工具")
            return 1

        # 检查输入文件是否存在（仅当提供了文件路径时）
        if material_path and not os.path.exists(material_path):
            print(f"❌ 错误: 输入文件不存在: {material_path}")
            return 1

        print(f"📖 输入材料: {material_path if material_path else '无（Chat模式）'}")
        print(f"🎯 用户目的: {purpose}")

        # 显示启用的智能工具
        tool_config = agent.enhancer.config
        enabled_tools = []
        if tool_config.get("video_recording"):
            enabled_tools.append("录屏工具")
        if tool_config.get("image_generation"):
            enabled_tools.append("图片生成工具")
        if tool_config.get("audio_synthesis"):
            enabled_tools.append("音频合成工具")
        if tool_config.get("timeline_generation"):
            enabled_tools.append("时间轴生成工具")

        if enabled_tools:
            print(f"🔧 可用工具池: {', '.join(enabled_tools)}")
            print("🧠 智能选择: 系统将根据内容和目标自动选择合适的工具")
        else:
            print("🔧 未启用任何工具")

        if output_file:
            print(f"📝 输出文件: {output_file}")

        # 执行处理
        result = agent.run(material_path, purpose, output_file, args.disable_tools)

        if result["status"] == "success":
            print("\n✅ 处理完成！")
            print(f"输入文件: {result['input_file']}")
            print(f"输出文件: {result['output_file']}")
            print(f"内容长度: {result['content_length']} 字符")
            print(f"架构特色: {result['message']}")
            print("📊 已通过工具类自动生成雷达图JSON数据和时间轴章节")
        else:
            print(f"\n❌ 处理失败: {result['error']}")
            return 1
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        return 1
    finally:
        if mcp_toolkit:
            mcp_toolkit.disconnect_sync()


if __name__ == "__main__":
    main()
