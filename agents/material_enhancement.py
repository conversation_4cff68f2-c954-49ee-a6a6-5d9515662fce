#!/usr/bin/env python3
"""
素材扩充模块 - 智能工具选择架构
从策略模式升级为工具模式，支持基于内容和目标的智能工具选择
工具分为四大类：内容结构化、多模态呈现、深度洞察、智能交互
"""

import json
import os
from typing import Any, Optional

from loguru import logger

# 从tools.enhance_tools包导入工具类
from tools.enhance_tools import (
    ArchitectureDiagramTool,
    ChartGenerationTool,
    CompetitiveAnalysisTool,
    CoreInfoExtractionTool,
    DeepInsightQATool,
    DeepInsightTool,
    EnhancementTool,
    ExampleExplainTool,
    MermaidDiagramTool,
    MindmapTool,
    ScreenRecordingTool,
    SixDimensionsEvaluationTool,
    TableGenerationTool,
    TimelineTool,
)


class LLMBasedToolSelector:
    """基于大模型的智能工具选择器"""

    def __init__(self, available_tools: list[EnhancementTool], model=None):
        self.available_tools = available_tools
        self.model = model

    def select_tools(self, content: str, purpose: str, context: dict[str, Any] = None) -> list[dict[str, Any]]:
        """使用LLM智能选择合适的工具"""
        if context is None:
            context = {}

        logger.info("🧠 开始基于LLM的智能工具选择")

        # 1. 首先做基本可用性检查
        available_tools = []
        for tool in self.available_tools:
            if tool.can_apply(content, purpose, context):
                available_tools.append(tool)
            else:
                logger.info(f"⏭️ 跳过工具 {tool.tool_name} - 基本条件不满足")

        if not available_tools:
            logger.info("⚠️ 无工具通过基本可用性检查")
            return []

        # 2. 使用LLM进行智能选择
        selected_tools = self._llm_select_tools(content, purpose, available_tools, context)

        logger.info(f"🎯 LLM工具选择完成，共选中 {len(selected_tools)} 个工具")
        return selected_tools

    def _llm_select_tools(
        self, content: str, purpose: str, available_tools: list[EnhancementTool], context: dict[str, Any]
    ) -> list[dict[str, Any]]:
        """使用LLM进行工具选择决策"""

        # 构建工具信息描述
        tools_description = ""
        for i, tool in enumerate(available_tools, 1):
            tool_info = tool.get_tool_info()
            tools_description += f"\n### 工具{i}: {tool_info['name']}\n"
            tools_description += f"**描述**: {tool_info['description']}\n"
            tools_description += f"**分类**: {tool_info['category']}\n"
            tools_description += f"**适用内容类型**: {', '.join(tool_info['suitable_content'])}\n"
            tools_description += f"**适用目标**: {', '.join(tool_info['suitable_purposes'])}\n"
            tools_description += f"**必需条件**: {', '.join(tool_info['required_conditions'])}\n"
            tools_description += f"**不适用内容**: {', '.join(tool_info['unsuitable_content'])}\n"
            tools_description += f"**不适用目标**: {', '.join(tool_info['unsuitable_purposes'])}\n"
            tools_description += f"**阻止条件**: {', '.join(tool_info['blocking_conditions'])}\n"
            tools_description += f"**使用场景**: {tool_info['use_case']}\n"

        # 生成当前配置状态信息
        config_status = self._generate_config_status_info(available_tools)

        # 构建LLM提示
        prompt = f"""
你是一个智能工具选择专家，需要根据内容和用户目标选择最合适的工具。

**用户目标**: {purpose}

**内容摘要**: {content[:10000]}{'...(内容截断)' if len(content) > 10000 else ''}

**当前配置状态**: {config_status}

**可用工具列表**:{tools_description}

**重要选择原则**:
1. **录屏工具优先原则**: 如果可用工具中包含录屏工具(screen_recording)，且已通过基本可用性检查，那么录屏工具应该被优先选择，因为它是制作开场视频介绍的标准工具。
   - **关键**: 请参考上面的"当前配置状态"信息来判断录屏工具是否可用
   - 如果配置状态显示"✅ 已配置且可用"，则录屏工具评分应该在85-95分之间
   - 录屏工具只录制config.yaml中material.sources里配置的URL，不录制用户输入内容中的URL
   - 此工具与用户输入的具体内容无关，只要配置文件中有URL就应该使用
   - **特别适合**: 工程项目介绍、GitHub项目展示、开源软件说明等场景

2. **深度洞察工具限制**: 深度洞察问答工具(deep_insight_qa)不适合工程项目介绍类内容
   - 该工具更适合理论分析、学术研究、概念解释等需要思辨的场景
   - 对于GitHub项目、技术产品、工程实现等实践性内容，应避免选择此工具

3. **工具适用性评估**: 仔细分析内容特点和用户目标，评估每个工具的适用性

4. **负面筛选**: 考虑工具的不适用场景，避免错误选择

5. **评分标准**: 为每个工具给出0-100的适用性评分
   - 录屏工具(有URL配置): 85-95分
   - 其他高度适用工具: 70-85分
   - 中等适用工具: 60-70分
   - 低适用工具: 40-60分
   - 不适用工具: 0-40分

6. **选择阈值**: 只选择评分>= 60的工具

7. **理由说明**: 为每个选择或拒绝决策提供清晰理由

**输出格式**:
请严格按照JSON格式输出，不要包含其他文本：

```json
{{
    "selected_tools": [
        {{
            "tool_name": "工具名称",
            "score": 评分(0-100),
            "reasoning": "选择理由"
        }}
    ],
    "rejected_tools": [
        {{
            "tool_name": "工具名称",
            "score": 评分(0-100),
            "reasoning": "拒绝理由"
        }}
    ],
    "analysis_summary": "整体分析总结"
}}
```
"""

        try:
            if self.model:
                from camel.agents import ChatAgent
                from camel.messages import BaseMessage

                # 创建临时ChatAgent来调用模型
                system_message = BaseMessage.make_assistant_message(
                    role_name="Tool Selector", content="你是一个智能工具选择专家，根据内容和用户目标选择最合适的工具。"
                )
                agent = ChatAgent(system_message=system_message, model=self.model)

                user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
                response = agent.step(user_message)

                if not response.msgs:
                    logger.error("AI代理未返回响应")
                    return self._fallback_selection(available_tools)

                response_content = response.msgs[0].content

                # 解析LLM响应
                selected_tools = self._parse_llm_response(response_content, available_tools)
                return selected_tools
            else:
                logger.warning("未提供LLM模型，使用简化选择逻辑")
                return self._fallback_selection(available_tools)

        except Exception as e:
            logger.error(f"LLM工具选择异常: {e}")
            return self._fallback_selection(available_tools)

    def _parse_llm_response(self, response: str, available_tools: list[EnhancementTool]) -> list[dict[str, Any]]:
        """解析LLM响应并构建选择结果"""
        try:
            # 提取JSON部分
            import re

            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析
                json_str = response.strip()

            result = json.loads(json_str)
            selected_tools = []

            # 构建工具名称到工具对象的映射
            tool_map = {tool.tool_name: tool for tool in available_tools}

            for tool_selection in result.get("selected_tools", []):
                tool_name = tool_selection["tool_name"]
                if tool_name in tool_map:
                    tool = tool_map[tool_name]
                    selected_tools.append(
                        {
                            "tool": tool,
                            "tool_info": tool.get_tool_info(),
                            "llm_decision": {
                                "score": tool_selection["score"],
                                "reasoning": tool_selection["reasoning"],
                                "selected": True,
                            },
                            "priority": tool_selection["score"],
                        }
                    )
                    logger.info(f"✅ LLM选中工具: {tool_name} (评分: {tool_selection['score']})")
                    logger.info(f"   理由: {tool_selection['reasoning']}")

            # 记录被拒绝的工具
            for tool_rejection in result.get("rejected_tools", []):
                tool_name = tool_rejection["tool_name"]
                logger.info(f"❌ LLM拒绝工具: {tool_name} (评分: {tool_rejection['score']})")
                logger.info(f"   理由: {tool_rejection['reasoning']}")

            logger.info(f"📊 LLM分析总结: {result.get('analysis_summary', '无总结')}")

            # 按评分排序
            selected_tools.sort(key=lambda x: x["priority"], reverse=True)
            return selected_tools

        except Exception as e:
            logger.error(f"解析LLM响应失败: {e}")
            logger.info(f"原始响应: {response[:500]}...")
            return self._fallback_selection(available_tools)

    def _fallback_selection(self, available_tools: list[EnhancementTool]) -> list[dict[str, Any]]:
        """备用选择逻辑（当LLM失败时）"""
        logger.info("🔄 使用备用选择逻辑")
        fallback_tools = []

        for tool in available_tools:
            # 录屏工具在备用选择中也给予更高的优先级
            if tool.tool_name == "screen_recording":
                priority_score = 90  # 录屏工具高优先级
                reasoning = "备用选择逻辑 - 录屏工具作为开场介绍的优先选择"
            else:
                priority_score = 70  # 其他工具默认分数
                reasoning = "备用选择逻辑 - 通过基本可用性检查"

            fallback_tools.append(
                {
                    "tool": tool,
                    "tool_info": tool.get_tool_info(),
                    "llm_decision": {"score": priority_score, "reasoning": reasoning, "selected": True},
                    "priority": priority_score,
                }
            )
            logger.info(f"🔄 备用选择: {tool.tool_name} (优先级: {priority_score})")

        # 按优先级排序，录屏工具排在前面
        fallback_tools.sort(key=lambda x: x["priority"], reverse=True)
        return fallback_tools

    def _generate_config_status_info(self, available_tools: list[EnhancementTool]) -> str:
        """生成当前配置状态的详细信息，帮助LLM了解工具的具体配置情况"""
        config_info = "以下工具已通过基本配置检查，具备使用条件："

        for tool in available_tools:
            config_info += f"\n- **{tool.tool_name}**: "

            if tool.tool_name == "screen_recording":
                # 为录屏工具提供详细的配置状态信息
                if hasattr(tool, "config") and tool.config:
                    material_enhance = tool.config.get("material", {}).get("material_enhance", {})
                    screen_record_enabled = material_enhance.get("screen_record", False)

                    sources = tool.config.get("material", {}).get("sources", {})
                    enabled_sources = []
                    for source_type, source_config in sources.items():
                        if source_config.get("enabled", False):
                            url = source_config.get("url", "")
                            if url and url.startswith(("http://", "https://")):
                                enabled_sources.append(f"{source_type}({url})")

                    if screen_record_enabled and enabled_sources:
                        config_info += (
                            f"✅ 已配置且可用 - screen_record=true, 启用的URL源: {', '.join(enabled_sources)}"
                        )
                    else:
                        config_info += f"❌ 配置不完整 - screen_record={screen_record_enabled}, 启用URL源: {len(enabled_sources)}个"
                else:
                    config_info += "❌ 配置缺失"
            else:
                # 对其他工具，简单说明已通过基本检查
                config_info += "✅ 基本条件满足"

        return config_info

    def get_tools_summary(self) -> str:
        """获取所有可用工具的摘要"""
        summary = "## 📋 可用工具列表\n\n"

        # 按类别组织工具
        categories = {}
        for tool in self.available_tools:
            category = tool.tool_category.value
            if category not in categories:
                categories[category] = []
            categories[category].append(tool)

        for category, tools in categories.items():
            summary += f"### {category}\n\n"
            for tool in tools:
                info = tool.get_tool_info()
                summary += f"**{info['name']}**: {info['description']}\n\n"

        return summary


class MaterialEnhancer:
    """素材扩充器 - 使用智能工具选择架构"""

    def __init__(
        self, enhancement_config: Optional[dict[str, bool]] = None, config_dict: Optional[dict] = None, model=None
    ):
        """
        初始化素材扩充器

        Args:
            enhancement_config: 扩充工具配置字典（命令行覆盖）
            config_dict: 完整配置字典（从config.yaml读取）
            model: LLM模型实例，用于智能工具选择
        """
        self.config_dict = config_dict or {}
        self.model = model

        # 优先使用命令行配置，其次使用config文件配置
        if enhancement_config:
            self.config = enhancement_config
        else:
            # 从config文件读取扩充配置
            material_enhance = self.config_dict.get("material", {}).get("material_enhance", {})
            self.config = {
                "video_recording": material_enhance.get("screen_record", False),
                "image_generation": material_enhance.get("image_generation", False),
                "audio_synthesis": material_enhance.get("audio_synthesis", False),
                "timeline_generation": material_enhance.get("timeline_generation", True),  # 默认启用
                "six_dimensions_evaluation": material_enhance.get("six_dimensions_evaluation", True),  # 默认启用
                "deep_insight_qa": material_enhance.get("deep_insight_qa", True),  # 默认启用
                "table_generation": material_enhance.get("table_generation", True),  # 默认启用
                "example_explain": material_enhance.get("example_explain", True),  # 默认启用
                "deep_insight": material_enhance.get("deep_insight", True),  # 默认启用
                "core_info_extraction": material_enhance.get("core_info_extraction", True),  # 默认启用
                "mindmap_generation": material_enhance.get("mindmap_generation", True),  # 默认启用
                "competitive_analysis": material_enhance.get("competitive_analysis", True),  # 默认启用
                "architecture_diagram": material_enhance.get("architecture_diagram", True),  # 默认启用
                "mermaid_diagram": material_enhance.get("mermaid_diagram", False),  # 默认启用
                "chart_generation": material_enhance.get("chart_generation", True),  # 默认启用
            }
        logger.info(f"读取到的扩充配置: {self.config}")

        self.tools = self._initialize_tools()
        self.tool_selector = LLMBasedToolSelector(self.tools, self.model)

    def _initialize_tools(self) -> list[EnhancementTool]:
        """根据配置初始化工具列表"""
        tools = []

        if self.config.get("video_recording", False):
            tools.append(ScreenRecordingTool(self.config_dict))
            logger.info("✅ 已启用录屏工具")

        if self.config.get("timeline_generation", True):  # 默认启用
            tools.append(TimelineTool(self.config_dict))
            logger.info("✅ 已启用时间轴工具")

        if self.config.get("six_dimensions_evaluation", True):  # 默认启用
            tools.append(SixDimensionsEvaluationTool(self.config_dict))
            logger.info("✅ 已启用六维度评估工具")

        if self.config.get("deep_insight_qa", True):  # 默认启用
            tools.append(DeepInsightQATool(self.config_dict))
            logger.info("✅ 已启用深度问答工具")

        if self.config.get("table_generation", True):  # 默认启用
            tools.append(TableGenerationTool(self.config_dict))
            logger.info("✅ 已启用表格生成工具")

        if self.config.get("example_explain", True):  # 默认启用
            tools.append(ExampleExplainTool(self.config_dict))
            logger.info("✅ 已启用举例说明工具")

        if self.config.get("deep_insight", True):  # 默认启用
            tools.append(DeepInsightTool(self.config_dict))
            logger.info("✅ 已启用深度洞察工具")

        if self.config.get("core_info_extraction", True):  # 默认启用
            tools.append(CoreInfoExtractionTool(self.config_dict))
            logger.info("✅ 已启用核心信息提取工具")

        if self.config.get("mindmap_generation", True):  # 默认启用
            tools.append(MindmapTool(self.config_dict))
            logger.info("✅ 已启用思维导图工具")

        if self.config.get("competitive_analysis", True):  # 默认启用
            tools.append(CompetitiveAnalysisTool(self.config_dict))
            logger.info("✅ 已启用竞品对比分析工具")

        if self.config.get("architecture_diagram", True):  # 默认启用
            tools.append(ArchitectureDiagramTool(self.config_dict))
            logger.info("✅ 已启用架构图生成工具")

        if self.config.get("mermaid_diagram", False):  # 默认不启用
            tools.append(MermaidDiagramTool(self.config_dict))
            logger.info("✅ 已启用Mermaid图表生成工具")

        if self.config.get("chart_generation", True):  # 默认启用
            tools.append(ChartGenerationTool(self.config_dict))
            logger.info("✅ 已启用图表生成工具")

        # TODO: 这里可以继续添加其他工具
        # if self.config.get("image_generation", False):
        #     tools.append(ImageGenerationTool())
        # if self.config.get("audio_synthesis", False):
        #     tools.append(AudioSynthesisTool())

        if not tools:
            logger.info("⚠️ 未启用任何扩充工具")

        return tools

    def enhance_material(
        self, original_content: str, material_content: str, purpose: str, output_dir: str = "output"
    ) -> dict[str, Any]:
        """执行智能素材扩充"""
        logger.info("🎬 开始智能素材扩充")

        if not self.tools:
            logger.info("⚠️ 无可用的扩充工具，跳过扩充步骤")
            return {"intro_content": "", "enhancements": [], "enhancement_count": 0}

        # 1. 基于大模型分析并插入工具调用标记（插桩）
        marked_content = self.analyze_and_inject_tool_markers(original_content, material_content, purpose)

        # 保存插桩后的内容到文件
        marked_content_file = os.path.join(output_dir, "marked_content.md")
        os.makedirs(os.path.dirname(marked_content_file), exist_ok=True)
        with open(marked_content_file, "w", encoding="utf-8") as f:
            f.write(marked_content)
        logger.info(f"✅ 已保存插桩内容到: {marked_content_file}")

        # 2. 遍历插桩内容，自动调用工具并融合结果
        enhanced_content, enhancements = self.apply_tools_and_merge(marked_content, output_dir)

        return {
            "enhanced_content": enhanced_content,
            "enhancements": enhancements,
            "enhancement_count": len(enhancements),
        }

    def apply_tools_and_merge(self, marked_content: str, output_dir: str = "output"):
        """
        解析插桩内容，遇到<<TOOL:{...}>>标记时自动调用工具，将结果插入对应位置。
        其余内容原样拼接，返回最终增强内容和所有工具调用结果。
        """
        import json
        import re

        pattern = r"<<TOOL:(\{.*?\})>>"
        pos = 0
        enhanced_parts = []
        enhancements = []
        for match in re.finditer(pattern, marked_content, flags=re.DOTALL):
            start, end = match.span()
            # 拼接前一段普通内容
            enhanced_parts.append(marked_content[pos:start])
            marker_json = match.group(1)
            try:
                marker = json.loads(marker_json)
                tool_name = marker.get("need_tool")
                tool_input = marker.get("tool_input")
                suggested_usage = marker.get("suggested_usage")
                # 查找工具
                tool = next((t for t in self.tools if getattr(t, "tool_name", None) == tool_name), None)
                if not tool:
                    tool_result = f"[⚠️ 未找到工具: {tool_name}]"
                    logger.warning(f"未找到工具: {tool_name}")
                else:
                    # 工具调用接口统一传入tool_input、output_dir，可根据实际需求扩展
                    logger.info(f"调用工具: {tool_name}")
                    try:
                        result = tool.apply_tool(tool_input, output_dir, context={})
                        logger.info(f"{tool_name} 调用结果: {result}")
                        enhancements.append(
                            {
                                "tool_name": tool_name,
                                "tool_input": tool_input,
                                "result": result,
                                "suggested_usage": suggested_usage,
                            }
                        )
                        tool_result = f"\n<补充素材>\n<工具名>\n{tool_name}\n</工具名>\n<用途>\n{suggested_usage}\n</用途>\n<工具输出>\n{result}\n</工具输出>\n</补充素材>"
                    except Exception as e:
                        tool_result = f"[⚠️ 工具调用异常: {tool_name} - {e}]"
                        logger.error(f"工具调用异常: {tool_name} - {e}")
                enhanced_parts.append(tool_result)
            except Exception as e:
                enhanced_parts.append(f"[⚠️ 工具标记解析失败: {e}]")
                logger.error(f"工具标记解析失败: {e}")
            pos = end
        # 拼接最后一段内容
        enhanced_parts.append(marked_content[pos:])
        enhanced_content = "".join(enhanced_parts)
        return enhanced_content, enhancements

    def analyze_and_inject_tool_markers(self, original_content: str, material_content: str, purpose: str) -> str:
        """
        基于大模型分析material_content，结合original_content和purpose，在合适位置插入工具调用标记。
        返回带插桩标记的完整文本（方式A）。
        """
        if self.model:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage

            # 1. 构造system prompt
            system_message = BaseMessage.make_assistant_message(
                role_name="Content Enhancement Expert",
                content="你是一个内容增强与插桩专家。请根据用户的讲解目的(purpose),分析讲解内容(material_content), 在需要扩展的地方插入工具调用标记，并从原始素材(original_content)中提取工具输入内容",
            )
            agent = ChatAgent(system_message=system_message, model=self.model)

            # 2. 构造可用工具信息
            import json

            tool_infos = []
            for tool in self.tools:
                try:
                    info = tool.get_tool_info()
                    if isinstance(info, dict):
                        info = json.dumps(info, ensure_ascii=False, indent=2)
                    tool_infos.append(info)
                except Exception as e:
                    logger.warning(f"工具 {getattr(tool, 'tool_name', str(tool))} 获取信息失败: {e}")
            tool_infos_str = "\n\n".join(tool_infos)

            # 3. 构造user prompt
            prompt = f"""
讲解内容(material_content):
{material_content}

讲解目的(purpose):
{purpose}

可用工具列表：
{tool_infos_str}

原始素材(original_content):
{original_content[:10000] if len(original_content) > 10000 else original_content}

请分析上述讲解内容(material_content)，结合可用工具，在讲解内容(material_content)里需要扩展的地方插入如下格式的特殊标记：
<<TOOL:{{\"need_tool\": \"工具名\", \"tool_input\": \"工具输入内容（从原始素材中提取，用中文）\", \"suggested_usage\": \"工具的输出如何辅助讲解\"}}>>

必须遵循以下规则：
1. 必须在讲解内容(material_content)里需要扩展的地方插入工具调用标记，**不要**在原始素材(original_content)里插入。
2. 工具输入内容部分根据工具的需要，从原始素材（original_content）中提取，如果需要简化，必须确保材料的完整性和准确性，不能是“原始素材”、“配置里的地址”之类的描述。
3. 最终输出的是讲解内容(material_content)+工具调用标记，讲解内容不要有任何的遗漏或修改，只需要在合适的地方插入工具调用标记。"""

            logger.info(f"用户提示长度：{len(prompt)}，单词数：{len(prompt.split())}")
            user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
            response = agent.step(user_message)

            if not response.msgs:
                logger.error("AI代理未返回插桩响应，降级为原内容")
                return material_content

            response_content = response.msgs[0].content
            return response_content

    def _generate_enhancement_appendix(self, enhancements: list[dict[str, Any]]) -> str:
        """生成扩充素材附录部分"""
        if not enhancements:
            return ""

        appendix = "## 📎 扩充素材\n\n"
        appendix += "以下是系统智能生成的扩充素材，为原始内容提供补充信息和多维度分析。\n\n"

        # 直接使用每个工具的原有输出格式
        for enhancement in enhancements:
            tool_name = enhancement.get("tool_name", "")

            # 找到对应的工具实例
            tool = None
            for t in self.tools:
                if t.tool_name == tool_name:
                    tool = t
                    break

            if tool:
                try:
                    # 直接使用工具的原有generate_intro方法
                    tool_content = tool.generate_intro(enhancement)
                    if tool_content:
                        appendix += tool_content + "\n"
                except Exception as e:
                    logger.warning(f"生成工具 {tool_name} 附录内容时出错: {e}")

        return appendix


# 便捷的工具注册器
class ToolRegistry:
    """工具注册器 - 方便动态管理工具"""

    _tools = {
        "screen_recording": ScreenRecordingTool,
        "timeline_generation": TimelineTool,
        "six_dimensions_evaluation": SixDimensionsEvaluationTool,
        "deep_insight_qa": DeepInsightQATool,
        "table_generation": TableGenerationTool,
        "example_explain": ExampleExplainTool,
        "deep_insight": DeepInsightTool,
        "core_info_extraction": CoreInfoExtractionTool,
        "mindmap_generation": MindmapTool,
        "competitive_analysis": CompetitiveAnalysisTool,
        "architecture_diagram": ArchitectureDiagramTool,
        "mermaid_diagram": MermaidDiagramTool,
        "chart_generation": ChartGenerationTool,
        # TODO: 添加更多工具类
        # "image_generation": ImageGenerationTool,
        # "audio_synthesis": AudioSynthesisTool,
    }

    @classmethod
    def register_tool(cls, name: str, tool_class):
        """注册新的工具类"""
        cls._tools[name] = tool_class
        logger.info(f"📝 已注册新工具: {name}")

    @classmethod
    def get_tool_class(cls, name: str):
        """获取工具类"""
        return cls._tools.get(name)

    @classmethod
    def list_tools(cls) -> list[str]:
        """列出所有可用工具"""
        return list(cls._tools.keys())

    @classmethod
    def create_enhancer_from_names(
        cls, tool_names: list[str], config_dict: dict = None, model=None
    ) -> MaterialEnhancer:
        """从工具名称列表创建扩充器"""
        config = {name: True for name in tool_names if name in cls._tools}
        return MaterialEnhancer(config, config_dict, model)
