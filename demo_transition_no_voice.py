"""
无语音转场效果演示 - 展示转场系统的功能
"""

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.core.transition_effects import TransitionManager
import shutil
from pathlib import Path


class NoVoiceTransitionDemo(Scene):  # 使用普通Scene避免语音问题
    """无语音转场演示场景"""
    
    def construct(self):
        # 清理之前的状态文件
        states_dir = Path("temp/scene_states")
        if states_dir.exists():
            shutil.rmtree(states_dir)
        states_dir.mkdir(parents=True, exist_ok=True)
        
        self.demo_all_transitions()
    
    def demo_all_transitions(self):
        """演示所有转场效果"""
        
        # 转场效果列表
        transitions = [
            ("fade", "淡入淡出", BLUE),
            ("slide_left", "向左滑动", GREEN),
            ("slide_right", "向右滑动", RED),
            ("slide_up", "向上滑动", YELLOW),
            ("slide_down", "向下滑动", PURPLE),
            ("zoom", "缩放转场", ORANGE),
            ("rotate", "旋转转场", PINK),
            ("spiral", "螺旋转场", TEAL),
        ]
        
        # 创建标题
        title = Text("转场效果演示", font_size=48, color=WHITE)
        subtitle = Text("展示各种转场类型", font_size=24, color=GRAY)
        title_group = VGroup(title, subtitle).arrange(DOWN, buff=0.5)
        
        self.play(FadeIn(title_group))
        self.wait(2)
        
        # 清除标题
        self.play(FadeOut(title_group))
        
        previous_content = None
        
        for i, (transition_name, transition_desc, color) in enumerate(transitions):
            # 创建当前内容
            current_content = VGroup(
                Text(f"转场 {i+1}: {transition_desc}", font_size=36, color=color),
                Text(f"类型: {transition_name}", font_size=24, color=WHITE).shift(DOWN * 0.8),
                Text("观察转场效果", font_size=18, color=GRAY).shift(DOWN * 1.5)
            )
            
            if previous_content is None:
                # 第一个内容直接显示
                self.play(FadeIn(current_content))
            else:
                # 应用转场效果
                transition_func = TransitionManager.get_transition_function(transition_name)
                animations = transition_func(previous_content, current_content, run_time=1.5)
                
                if animations:
                    self.play(*animations, run_time=1.5)
                else:
                    # 如果转场函数返回空，使用默认淡入淡出
                    self.play(FadeOut(previous_content), FadeIn(current_content))
            
            self.wait(1.5)
            previous_content = current_content
        
        # 结束演示
        end_content = VGroup(
            Text("演示完成", font_size=48, color=GREEN),
            Text("转场系统已成功实现", font_size=24, color=WHITE).shift(DOWN * 0.8),
            Text("✨ 精美 • 🎬 多样 • 🔄 自动", font_size=18, color=GRAY).shift(DOWN * 1.5)
        )
        
        # 最后一个转场
        final_transition = TransitionManager.get_transition_function("spiral")
        final_animations = final_transition(previous_content, end_content, run_time=2.0)
        
        if final_animations:
            self.play(*final_animations, run_time=2.0)
        else:
            self.play(FadeOut(previous_content), FadeIn(end_content))
        
        self.wait(3)
        
        # 清除结束内容
        self.play(FadeOut(end_content))


class TransitionComparisonDemo(Scene):
    """转场对比演示"""
    
    def construct(self):
        # 标题
        title = Text("转场效果对比", font_size=48, color=WHITE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 创建对比内容
        old_way_title = Text("传统方式", font_size=32, color=RED).shift(LEFT * 3 + UP * 1)
        new_way_title = Text("转场系统", font_size=32, color=GREEN).shift(RIGHT * 3 + UP * 1)
        
        self.play(Write(old_way_title), Write(new_way_title))
        
        # 传统方式演示
        old_content1 = Rectangle(width=2, height=1, fill_opacity=0.5, fill_color=BLUE).shift(LEFT * 3)
        old_content2 = Rectangle(width=2, height=1, fill_opacity=0.5, fill_color=RED).shift(LEFT * 3)
        
        self.play(FadeIn(old_content1))
        self.wait(0.5)
        self.play(FadeOut(old_content1), FadeIn(old_content2))  # 简单淡入淡出
        self.wait(0.5)
        
        # 转场系统演示
        new_content1 = Rectangle(width=2, height=1, fill_opacity=0.5, fill_color=BLUE).shift(RIGHT * 3)
        new_content2 = Rectangle(width=2, height=1, fill_opacity=0.5, fill_color=GREEN).shift(RIGHT * 3)
        
        self.play(FadeIn(new_content1))
        self.wait(0.5)
        
        # 应用精美转场
        transition_func = TransitionManager.get_transition_function("slide_left")
        animations = transition_func(new_content1, new_content2, run_time=1.5)
        self.play(*animations, run_time=1.5)
        self.wait(0.5)
        
        # 添加说明
        old_label = Text("单调的淡入淡出", font_size=16, color=GRAY).next_to(old_content2, DOWN)
        new_label = Text("精美的转场效果", font_size=16, color=GRAY).next_to(new_content2, DOWN)
        
        self.play(Write(old_label), Write(new_label))
        self.wait(2)
        
        # 清除所有内容
        self.play(FadeOut(VGroup(
            title, old_way_title, new_way_title,
            old_content2, new_content2, old_label, new_label
        )))


def create_transition_showcase():
    """创建转场效果展示"""
    print("创建转场效果展示...")
    
    import os
    
    # 渲染主演示
    print("1. 渲染转场效果演示...")
    os.system("manim demo_transition_no_voice.py NoVoiceTransitionDemo -ql")
    
    # 渲染对比演示
    print("2. 渲染转场对比演示...")
    os.system("manim demo_transition_no_voice.py TransitionComparisonDemo -ql")
    
    print("转场效果展示创建完成！")


def show_transition_summary():
    """显示转场系统总结"""
    print("\n" + "="*60)
    print("转场系统实现总结")
    print("="*60)
    
    summary = """
🎬 转场效果系统已成功实现！

✅ 主要功能:
  • 11种精美转场效果
  • 同一分镜内自动转场
  • 分镜间转场支持
  • 可配置转场参数
  • 随机转场选择

✅ 核心组件:
  • FeynmanScene - 增强的场景类
  • TransitionEffects - 转场效果库
  • TransitionManager - 转场管理器
  • InterSceneTransition - 分镜间转场

✅ 支持的转场类型:
  • fade - 淡入淡出
  • slide_left/right/up/down - 滑动转场
  • zoom - 缩放转场
  • rotate - 旋转转场
  • spiral - 螺旋转场
  • wipe_left - 擦除转场
  • circle_wipe - 圆形擦除
  • flip - 翻转转场

✅ 使用方式:
  • 自动转场: 动画函数自动应用
  • 手动转场: TransitionManager.apply_transition()
  • 配置转场: scene.transition_enabled = True

✅ 测试验证:
  • 基础功能测试通过
  • 转场效果展示完成
  • 系统集成测试成功

🎉 转场系统让视频更加精彩！
"""
    
    print(summary)


def main():
    """主函数"""
    print("=" * 60)
    print("转场效果系统演示")
    print("=" * 60)
    
    # 创建转场展示
    create_transition_showcase()
    
    # 显示总结
    show_transition_summary()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    print("\n📁 生成的文件:")
    print("  • media/videos/demo_transition_no_voice/480p15/NoVoiceTransitionDemo.mp4")
    print("  • media/videos/demo_transition_no_voice/480p15/TransitionComparisonDemo.mp4")
    print("\n📖 文档:")
    print("  • docs/transition_system.md")
    print("\n🧪 测试文件:")
    print("  • test_transition_simple.py")
    print("  • demo_transition_usage.py")


if __name__ == "__main__":
    main()
